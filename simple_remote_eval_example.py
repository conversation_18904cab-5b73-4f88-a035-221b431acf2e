"""
Simple Remote Eval Example

This example demonstrates how to create a remote evaluation that can be run
from the Braintrust Playground. It shows:

1. Basic task implementation
2. Configurable parameters
3. Custom scoring
4. How to run the remote eval server

To run this example:
1. Save this file as simple_remote_eval_example.py
2. Run: braintrust eval simple_remote_eval_example.py --dev
3. The server will start at http://localhost:8300
4. Add this URL to your project's remote eval sources in Braintrust
5. Use it from the playground!
"""

import asyncio
from typing import List, Optional
from autoevals import <PERSON>enshteinScorer
from braintrust import Eval, init_dataset, wrap_openai, current_span, traced
import openai
from pydantic import BaseModel, Field

# Set up OpenAI client (make sure OPENAI_API_KEY is in your environment)
client = wrap_openai(openai.AsyncOpenAI())


# Parameter validation models (optional but recommended)
class TemperatureParam(BaseModel):
    """Validator for temperature parameter."""
    value: float = Field(
        default=0.7, 
        ge=0.0, 
        le=2.0, 
        description="Controls randomness in the response (0.0 = deterministic, 2.0 = very random)"
    )


class MaxTokensParam(BaseModel):
    """Validator for max tokens parameter."""
    value: int = Field(
        default=150, 
        ge=1, 
        le=4000, 
        description="Maximum number of tokens in the response"
    )


class SystemMessageParam(BaseModel):
    """Validator for system message parameter."""
    value: str = Field(
        default="You are a helpful assistant that provides clear, concise answers.",
        description="System message that sets the AI's behavior"
    )


@traced("preprocessing")
def preprocess_input(input_text: str, system_message: str):
    """
    Demonstrates nested logging with @traced decorator.
    All logs from this function will appear as child spans in Braintrust.
    """
    # Log the preprocessing step
    current_span().log(
        input={"raw_input": input_text, "system_message": system_message},
        metadata={"step": "preprocessing", "input_length": len(input_text)}
    )

    # Simulate some preprocessing logic
    processed_input = input_text.strip().lower()

    # Log the result
    current_span().log(
        output={"processed_input": processed_input},
        metadata={"processing_applied": "strip and lowercase"}
    )

    return processed_input


@traced("api_call")
async def make_api_call(messages: list, temperature: float, max_tokens: int):
    """
    Demonstrates API call logging with detailed metrics.
    This will create a separate span for the API call.
    """
    # Log the API request details
    current_span().log(
        input={"messages": messages, "model_params": {"temperature": temperature, "max_tokens": max_tokens}},
        metadata={"api_provider": "openai", "model": "gpt-3.5-turbo"}
    )

    try:
        response = await client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=messages,
            temperature=temperature,
            max_tokens=max_tokens
        )

        answer = response.choices[0].message.content or ""

        # Log detailed API response metrics
        current_span().log(
            output=answer,
            metrics={
                "prompt_tokens": response.usage.prompt_tokens if response.usage else 0,
                "completion_tokens": response.usage.completion_tokens if response.usage else 0,
                "total_tokens": response.usage.total_tokens if response.usage else 0,
                "latency": 0.5  # You could measure actual latency
            },
            metadata={
                "finish_reason": response.choices[0].finish_reason,
                "model_used": response.model
            }
        )

        return answer

    except Exception as e:
        # Log API errors with details
        current_span().log(
            error=str(e),
            metadata={"error_type": type(e).__name__, "step": "api_call"}
        )
        raise


async def simple_qa_task(input_text: str, hooks):
    """
    Enhanced Q&A task that demonstrates comprehensive logging in remote evals.

    This shows how ALL logs created in your remote eval code are captured
    and sent to Braintrust, including:
    - Main task logs via hooks.span.log()
    - Nested function logs via @traced decorator
    - Custom span creation and logging
    - Error logging and metrics
    """
    parameters = hooks.parameters

    # Get parameters with defaults
    temperature = parameters.get("temperature", 0.7)
    max_tokens = parameters.get("max_tokens", 150)
    system_message = parameters.get("system_message", "You are a helpful assistant.")

    # 1. Main task logging - this appears in the main evaluation span
    hooks.span.log(
        metadata={
            "step": "task_start",
            "temperature": temperature,
            "max_tokens": max_tokens,
            "system_message": system_message[:50] + "..." if len(system_message) > 50 else system_message
        }
    )

    try:
        # 2. Preprocessing with nested logging - creates a child span
        processed_input = preprocess_input(input_text, system_message)

        # 3. Create a custom span for validation
        with current_span().start_span("input_validation") as validation_span:
            is_valid = len(processed_input.strip()) > 0
            validation_span.log(
                input={"processed_input": processed_input},
                output={"is_valid": is_valid},
                metadata={"validation_rule": "non_empty_after_processing"}
            )

            if not is_valid:
                validation_span.log(error="Input is empty after preprocessing")
                return "Error: Invalid input"

        # 4. API call with detailed logging - creates another child span
        messages = [
            {"role": "system", "content": system_message},
            {"role": "user", "content": processed_input}
        ]

        answer = await make_api_call(messages, temperature, max_tokens)

        # 5. Post-processing logging
        with current_span().start_span("postprocessing") as post_span:
            word_count = len(answer.split())
            char_count = len(answer)

            post_span.log(
                input={"raw_answer": answer},
                output={"word_count": word_count, "char_count": char_count},
                metrics={
                    "response_word_count": word_count,
                    "response_char_count": char_count
                },
                metadata={"processing_step": "analysis"}
            )

        # 6. Final task logging
        hooks.span.log(
            output=answer,
            metadata={
                "step": "task_complete",
                "final_word_count": word_count,
                "processing_chain": ["preprocess", "validate", "api_call", "postprocess"]
            }
        )

        return answer

    except Exception as e:
        # Error logging with context
        hooks.span.log(
            error=str(e),
            metadata={
                "step": "task_error",
                "error_type": type(e).__name__,
                "input_length": len(input_text)
            }
        )
        return f"Error: {str(e)}"


def custom_length_scorer(input: str, output: str, expected: Optional[str] = None, **kwargs) -> dict:
    """
    Custom scorer that demonstrates logging within scoring functions.

    Even logs created in custom scorers are captured and sent to Braintrust!
    """
    # Create a custom span for scoring logic
    with current_span().start_span("length_scoring") as scoring_span:
        length = len(output.split())
        expected_length = len(expected.split()) if expected else None

        # Log the scoring inputs
        scoring_span.log(
            input={
                "eval_input": input,
                "output": output,
                "expected": expected,
                "output_word_count": length,
                "expected_word_count": expected_length
            },
            metadata={"scorer": "length_appropriateness", "step": "input_analysis"}
        )

        # Score based on response length (prefer 10-100 words)
        if 10 <= length <= 100:
            score = 1.0
            rationale = f"Perfect length: {length} words (optimal range: 10-100)"
        elif length < 10:
            score = max(0.0, length / 10.0)  # Penalize very short responses
            rationale = f"Too short: {length} words (penalty applied)"
        else:
            score = max(0.0, 1.0 - (length - 100) / 200.0)  # Penalize very long responses
            rationale = f"Too long: {length} words (penalty applied)"

        # Log the scoring result
        scoring_span.log(
            output={
                "score": score,
                "rationale": rationale
            },
            metrics={
                "length_score": score,
                "word_count": length
            },
            metadata={"scoring_rule": "10-100_words_optimal"}
        )

        return {
            "name": "length_appropriateness",
            "score": score,
            "metadata": {
                "word_count": length,
                "expected_word_count": expected_length,
                "rationale": rationale,
                "input_question": input[:100] + "..." if len(input) > 100 else input
            }
        }


# Define the remote evaluation
Eval(
    "Simple Q&A Remote Eval",
    data=init_dataset("local dev", name="qa_examples"),  # Dataset is ignored in remote evals
    task=simple_qa_task,
    scores=[
        LevenshteinScorer,  # Built-in similarity scorer
        custom_length_scorer,  # Custom scorer
    ],
    parameters={
        # Simple parameters
        "temperature": TemperatureParam,
        "max_tokens": MaxTokensParam, 
        "system_message": SystemMessageParam,
        
        # You can also define parameters inline without Pydantic models
        "include_examples": {
            "type": "boolean",
            "description": "Whether to include examples in the prompt",
            "default": False
        }
    },
    metadata={
        "description": "A simple Q&A evaluation that demonstrates remote eval capabilities",
        "version": "1.0",
        "author": "Your Name"
    }
)

"""
How to use this remote eval:

1. Start the dev server:
   braintrust eval simple_remote_eval_example.py --dev

2. The server will start at http://localhost:8300

3. In Braintrust:
   - Go to your project's Configuration > Remote evals
   - Add "http://localhost:8300" as a remote eval source
   - Go to a playground and click "+ Remote" in the Task pane
   - Select "Simple Q&A Remote Eval"

4. Test with sample data like:
   [
     {"input": "What is the capital of France?", "expected": "Paris"},
     {"input": "Explain photosynthesis briefly", "expected": "Process where plants convert sunlight to energy"},
     {"input": "What is 2+2?", "expected": "4"}
   ]

5. Adjust parameters in the playground:
   - Change temperature to see how it affects randomness
   - Modify max_tokens to control response length
   - Update system_message to change the AI's behavior

The evaluation will run on your local machine but results will be tracked
in Braintrust, allowing you to compare different parameter configurations
and analyze performance over time.
"""
