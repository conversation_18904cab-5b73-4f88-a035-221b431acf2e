"""
Simple Remote Eval Example

This example demonstrates how to create a remote evaluation that can be run
from the Braintrust Playground. It shows:

1. Basic task implementation
2. Configurable parameters
3. Custom scoring
4. How to run the remote eval server

To run this example:
1. Save this file as simple_remote_eval_example.py
2. Run: braintrust eval simple_remote_eval_example.py --dev
3. The server will start at http://localhost:8300
4. Add this URL to your project's remote eval sources in Braintrust
5. Use it from the playground!
"""

import asyncio
from typing import List, Optional
from autoevals import <PERSON><PERSON><PERSON><PERSON>
from braintrust import Eval, init_dataset, wrap_openai
import openai
from pydantic import BaseModel, Field

# Set up OpenAI client (make sure OPENAI_API_KEY is in your environment)
client = wrap_openai(openai.AsyncOpenAI())


# Parameter validation models (optional but recommended)
class TemperatureParam(BaseModel):
    """Validator for temperature parameter."""
    value: float = Field(
        default=0.7, 
        ge=0.0, 
        le=2.0, 
        description="Controls randomness in the response (0.0 = deterministic, 2.0 = very random)"
    )


class MaxTokensParam(BaseModel):
    """Validator for max tokens parameter."""
    value: int = Field(
        default=150, 
        ge=1, 
        le=4000, 
        description="Maximum number of tokens in the response"
    )


class SystemMessageParam(BaseModel):
    """Validator for system message parameter."""
    value: str = Field(
        default="You are a helpful assistant that provides clear, concise answers.",
        description="System message that sets the AI's behavior"
    )


async def simple_qa_task(input_text: str, hooks):
    """
    A simple Q&A task that uses OpenAI to answer questions.
    
    This demonstrates:
    - Using parameters from the playground
    - Making API calls with configurable settings
    - Returning structured output
    """
    parameters = hooks.parameters
    
    # Get parameters with defaults
    temperature = parameters.get("temperature", 0.7)
    max_tokens = parameters.get("max_tokens", 150)
    system_message = parameters.get("system_message", "You are a helpful assistant.")
    
    # Log some metadata for debugging
    hooks.span.log(
        metadata={
            "temperature": temperature,
            "max_tokens": max_tokens,
            "system_message": system_message[:50] + "..." if len(system_message) > 50 else system_message
        }
    )
    
    try:
        # Make the API call
        response = await client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": system_message},
                {"role": "user", "content": input_text}
            ],
            temperature=temperature,
            max_tokens=max_tokens
        )
        
        answer = response.choices[0].message.content or ""
        
        # Log the response for analysis
        hooks.span.log(
            output=answer,
            metadata={"tokens_used": response.usage.total_tokens if response.usage else None}
        )
        
        return answer
        
    except Exception as e:
        # Log errors for debugging
        hooks.span.log(error=str(e))
        return f"Error: {str(e)}"


def custom_length_scorer(output: str, expected: Optional[str] = None, **kwargs) -> dict:
    """
    Custom scorer that checks if the response length is appropriate.
    
    This demonstrates how to create custom scoring functions.
    """
    length = len(output.split())
    
    # Score based on response length (prefer 10-100 words)
    if 10 <= length <= 100:
        score = 1.0
    elif length < 10:
        score = max(0.0, length / 10.0)  # Penalize very short responses
    else:
        score = max(0.0, 1.0 - (length - 100) / 200.0)  # Penalize very long responses
    
    return {
        "name": "length_appropriateness",
        "score": score,
        "metadata": {
            "word_count": length,
            "rationale": f"Response has {length} words. Optimal range is 10-100 words."
        }
    }


# Define the remote evaluation
Eval(
    "Simple Q&A Remote Eval",
    data=init_dataset("local dev", name="qa_examples"),  # Dataset is ignored in remote evals
    task=simple_qa_task,
    scores=[
        Levenshtein,  # Built-in similarity scorer
        custom_length_scorer,  # Custom scorer
    ],
    parameters={
        # Simple parameters
        "temperature": TemperatureParam,
        "max_tokens": MaxTokensParam, 
        "system_message": SystemMessageParam,
        
        # You can also define parameters inline without Pydantic models
        "include_examples": {
            "type": "boolean",
            "description": "Whether to include examples in the prompt",
            "default": False
        }
    },
    metadata={
        "description": "A simple Q&A evaluation that demonstrates remote eval capabilities",
        "version": "1.0",
        "author": "Your Name"
    }
)

"""
How to use this remote eval:

1. Start the dev server:
   braintrust eval simple_remote_eval_example.py --dev

2. The server will start at http://localhost:8300

3. In Braintrust:
   - Go to your project's Configuration > Remote evals
   - Add "http://localhost:8300" as a remote eval source
   - Go to a playground and click "+ Remote" in the Task pane
   - Select "Simple Q&A Remote Eval"

4. Test with sample data like:
   [
     {"input": "What is the capital of France?", "expected": "Paris"},
     {"input": "Explain photosynthesis briefly", "expected": "Process where plants convert sunlight to energy"},
     {"input": "What is 2+2?", "expected": "4"}
   ]

5. Adjust parameters in the playground:
   - Change temperature to see how it affects randomness
   - Modify max_tokens to control response length
   - Update system_message to change the AI's behavior

The evaluation will run on your local machine but results will be tracked
in Braintrust, allowing you to compare different parameter configurations
and analyze performance over time.
"""
