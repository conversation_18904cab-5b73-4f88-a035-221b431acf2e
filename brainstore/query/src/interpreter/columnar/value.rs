use std::{
    hash::{<PERSON>h, <PERSON>her},
    io::Write,
};

use btql::{binder::ast::ArithmeticOp, interpreter::context::ExprContext, typesystem::CastInto};

use time::format_description::well_known::Rfc3339;
use util::{
    ptree::{MakeP<PERSON>ree, TreeBuilder},
    xact::PaginationKey,
};

use crate::interpreter::{tantivy::columnstore::batch_ordinals_to_buffers, InterpreterContext};

use super::{expr::StringDecompressorState, hash_map::FastHashMap};

// This is TantivyFastValueType + string and bytes
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, MakePTree, PartialEq)]
pub enum ColumnarValueType {
    Bool,
    I64,
    U64,
    F64,
    DateTime,
    StringPtr, // There are no literal byte values, so we don't need BytesPtr
    StringOrdinal,
    BytesOrdinal,
    PaginationKey,
    // This means we don't know the column's type at compile time, and it may vary
    // per segment. Only certain expressions can handle dynamic types.
    Dynamic,
    // This means we know that the column is empty, and always returns null.
    Null,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy)]
pub enum ColumnarValueLiteralBorrowed {
    Null,
    Bool(bool),
    I64(i64),
    U64(u64),
    F64(f64),
    PaginationKey(PaginationKey),
    DateTime(tantivy::DateTime),
    StringPtr(PtrOffset),
    StringOrdinal(StringOrdinal),
    BytesOrdinal(BytesOrdinal),
}

#[derive(Debug, Clone)]
pub enum ColumnarValueLiteralOwned {
    Null,
    Bool(bool),
    I64(i64),
    U64(u64),
    F64(f64),
    PaginationKey(PaginationKey),
    DateTime(tantivy::DateTime),
    String(String),
    Bytes(Vec<u8>),
}

impl MakePTree for ColumnarValueLiteralBorrowed {
    fn label(&self) -> String {
        match self {
            ColumnarValueLiteralBorrowed::Null => "null".to_string(),
            ColumnarValueLiteralBorrowed::Bool(val) => format!("{}", val),
            ColumnarValueLiteralBorrowed::I64(val) => format!("{}", val),
            ColumnarValueLiteralBorrowed::U64(val) => format!("{}", val),
            ColumnarValueLiteralBorrowed::PaginationKey(val) => format!("{}", val),
            ColumnarValueLiteralBorrowed::F64(val) => format!("{}", val),
            ColumnarValueLiteralBorrowed::DateTime(val) => format!("{:?}", val),
            ColumnarValueLiteralBorrowed::StringPtr(val) => format!("{:?}", val),
            ColumnarValueLiteralBorrowed::StringOrdinal(val) => {
                format!("string_ordinal({:?})", val)
            }
            ColumnarValueLiteralBorrowed::BytesOrdinal(val) => format!("bytes_ordinal({:?})", val),
        }
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl ColumnarValueLiteralBorrowed {
    pub fn value_type(&self) -> ColumnarValueType {
        match self {
            ColumnarValueLiteralBorrowed::Null => ColumnarValueType::Null,
            ColumnarValueLiteralBorrowed::Bool(_) => ColumnarValueType::Bool,
            ColumnarValueLiteralBorrowed::I64(_) => ColumnarValueType::I64,
            ColumnarValueLiteralBorrowed::U64(_) => ColumnarValueType::U64,
            ColumnarValueLiteralBorrowed::PaginationKey(_) => ColumnarValueType::PaginationKey,
            ColumnarValueLiteralBorrowed::F64(_) => ColumnarValueType::F64,
            ColumnarValueLiteralBorrowed::DateTime(_) => ColumnarValueType::DateTime,
            ColumnarValueLiteralBorrowed::StringPtr(_) => ColumnarValueType::StringPtr,
            ColumnarValueLiteralBorrowed::StringOrdinal(_) => ColumnarValueType::StringOrdinal,
            ColumnarValueLiteralBorrowed::BytesOrdinal(_) => ColumnarValueType::BytesOrdinal,
        }
    }
}

impl PartialEq for ColumnarValueLiteralOwned {
    fn eq(&self, other: &Self) -> bool {
        match (self, other) {
            (ColumnarValueLiteralOwned::Null, ColumnarValueLiteralOwned::Null) => true,
            (ColumnarValueLiteralOwned::Bool(a), ColumnarValueLiteralOwned::Bool(b)) => a == b,
            (ColumnarValueLiteralOwned::I64(a), ColumnarValueLiteralOwned::I64(b)) => a == b,
            (ColumnarValueLiteralOwned::U64(a), ColumnarValueLiteralOwned::U64(b)) => a == b,
            (
                ColumnarValueLiteralOwned::PaginationKey(a),
                ColumnarValueLiteralOwned::PaginationKey(b),
            ) => a == b,
            (ColumnarValueLiteralOwned::F64(a), ColumnarValueLiteralOwned::F64(b)) => {
                a.to_bits() == b.to_bits()
            }
            (ColumnarValueLiteralOwned::DateTime(a), ColumnarValueLiteralOwned::DateTime(b)) => {
                a == b
            }
            (ColumnarValueLiteralOwned::String(a), ColumnarValueLiteralOwned::String(b)) => a == b,
            (ColumnarValueLiteralOwned::Bytes(a), ColumnarValueLiteralOwned::Bytes(b)) => a == b,
            _ => false,
        }
    }
}

impl Eq for ColumnarValueLiteralOwned {}

impl std::hash::Hash for ColumnarValueLiteralOwned {
    fn hash<H: std::hash::Hasher>(&self, state: &mut H) {
        match self {
            ColumnarValueLiteralOwned::Bool(v) => {
                0u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::I64(v) => {
                1u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::U64(v) => {
                2u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::PaginationKey(v) => {
                3u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::F64(v) => {
                4u8.hash(state);
                v.to_bits().hash(state);
            }
            ColumnarValueLiteralOwned::DateTime(v) => {
                5u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::String(v) => {
                6u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::Bytes(v) => {
                7u8.hash(state);
                v.hash(state);
            }
            ColumnarValueLiteralOwned::Null => {
                8u8.hash(state);
            }
        }
    }
}

impl MakePTree for ColumnarValueLiteralOwned {
    fn label(&self) -> String {
        match self {
            ColumnarValueLiteralOwned::Null => "null".to_string(),
            ColumnarValueLiteralOwned::Bool(val) => format!("{}", val),
            ColumnarValueLiteralOwned::I64(val) => format!("{}", val),
            ColumnarValueLiteralOwned::U64(val) => format!("{}", val),
            ColumnarValueLiteralOwned::PaginationKey(val) => format!("{}", val),
            ColumnarValueLiteralOwned::F64(val) => format!("{}", val),
            ColumnarValueLiteralOwned::DateTime(val) => format!("{:?}", val),
            ColumnarValueLiteralOwned::String(val) => val.to_string(),
            ColumnarValueLiteralOwned::Bytes(val) => format!("{:?}", val),
        }
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl ColumnarValueLiteralOwned {
    pub fn value_type(&self) -> ColumnarValueType {
        match self {
            ColumnarValueLiteralOwned::Null => ColumnarValueType::Null,
            ColumnarValueLiteralOwned::Bool(_) => ColumnarValueType::Bool,
            ColumnarValueLiteralOwned::I64(_) => ColumnarValueType::I64,
            ColumnarValueLiteralOwned::U64(_) => ColumnarValueType::U64,
            ColumnarValueLiteralOwned::PaginationKey(_) => ColumnarValueType::PaginationKey,
            ColumnarValueLiteralOwned::F64(_) => ColumnarValueType::F64,
            ColumnarValueLiteralOwned::DateTime(_) => ColumnarValueType::DateTime,
            ColumnarValueLiteralOwned::String(_) => ColumnarValueType::StringPtr,
            ColumnarValueLiteralOwned::Bytes(_) => ColumnarValueType::StringPtr,
        }
    }
}

pub struct ColumnarExprContext {
    expr_ctx: ExprContext,
    expr_strings: Vec<u8>,
    pub dictionaries: Vec<FastHashMap<u64, Vec<u8>>>,
}

impl std::fmt::Debug for ColumnarExprContext {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ColumnarExprContext").finish()
    }
}

impl ColumnarExprContext {
    pub fn from_interpreter_ctx(ctx: &InterpreterContext) -> Self {
        Self::new(ctx.expr_ctx.clone())
    }

    pub fn new(expr_context: ExprContext) -> Self {
        Self {
            expr_ctx: expr_context,
            expr_strings: Vec::new(),
            dictionaries: Vec::new(),
        }
    }

    pub fn expr_ctx(&self) -> &ExprContext {
        &self.expr_ctx
    }

    pub fn insert_expr_string(&mut self, string: &str) -> PtrOffset {
        let offset = self.expr_strings.len();
        self.expr_strings.extend_from_slice(string.as_bytes());

        // Switch the top bit to 1
        PtrOffset::new(offset, self.expr_strings.len() - offset)
    }

    pub fn insert_value_as_expr_string<T: std::fmt::Display>(&mut self, value: T) -> PtrOffset {
        let offset = self.expr_strings.len();
        write!(&mut self.expr_strings, "{}", value).unwrap();

        // Switch the top bit to 1
        PtrOffset::new(offset, self.expr_strings.len() - offset)
    }

    pub fn insert_datetime_string(&mut self, value: &tantivy::DateTime) -> Option<PtrOffset> {
        let offset = self.expr_strings.len();
        value
            .into_utc()
            .format_into(&mut self.expr_strings, &Rfc3339)
            .ok()?;

        // Switch the top bit to 1
        Some(PtrOffset::new(offset, self.expr_strings.len() - offset))
    }

    pub fn string_ordinal_to_ptr_offset(&mut self, ordinal: StringOrdinal) -> PtrOffset {
        // First get the dictionary entry
        let bytes = &self.dictionaries[ordinal.col as usize][&ordinal.ord];

        // Now insert it as an expr string
        let offset = self.expr_strings.len();
        self.expr_strings.extend_from_slice(bytes);

        PtrOffset::new(offset, bytes.len())
    }

    pub fn bytes_ordinal_to_ptr_offset(&mut self, ordinal: BytesOrdinal) -> Option<PtrOffset> {
        // First get the dictionary entry
        let bytes = &self.dictionaries[ordinal.col as usize][&ordinal.ord];

        // Validate UTF-8
        if std::str::from_utf8(bytes).is_err() {
            return None;
        }

        // Now insert it as an expr string
        let offset = self.expr_strings.len();
        self.expr_strings.extend_from_slice(bytes);

        Some(PtrOffset::new(offset, bytes.len()))
    }

    #[inline]
    pub fn get_string(&self, offset: &PtrOffset) -> &str {
        let bytes = self.get_bytes(offset);
        unsafe {
            // We must have already validated that this is a valid UTF-8 string, eg while decoding.
            std::str::from_utf8_unchecked(bytes)
        }
    }

    #[inline]
    pub fn get_bytes(&self, offset: &PtrOffset) -> &[u8] {
        debug_assert!(
            offset.offset < self.expr_strings.len() as u32
                && offset.offset + offset.length <= self.expr_strings.len() as u32,
            "expr_offset: {:?}, length: {:?}, expr_strings.len: {:?}",
            offset.offset,
            offset.length,
            self.expr_strings.len(),
        );
        &self.expr_strings[offset.offset as usize..(offset.offset + offset.length) as usize]
    }

    #[inline]
    pub fn get_bytes_from_ordinal(&self, ordinal: &BytesOrdinal) -> &[u8] {
        debug_assert!(
            self.dictionaries.len() > ordinal.col as usize,
            "{:?} was never decompressed (no dictionary)",
            ordinal
        );
        debug_assert!(
            self.dictionaries[ordinal.col as usize].contains_key(&ordinal.ord),
            "{:?} was never decompressed",
            ordinal
        );
        let bytes = self.dictionaries[ordinal.col as usize]
            .get(&ordinal.ord)
            .expect("bytes ordinal was never decompressed");
        bytes
    }

    #[inline]
    pub fn get_string_from_ordinal(&self, ordinal: &StringOrdinal) -> &str {
        let bytes = self.get_bytes_from_ordinal(ordinal);
        unsafe {
            // We must have already validated that this is a valid UTF-8 string, eg while decoding.
            std::str::from_utf8_unchecked(bytes)
        }
    }

    // This function very efficiently takes a set of ordinals and decompresses the novel ones
    // that are not already in the decompressor's dictionary. It assumes that strings are
    // already validated.
    pub fn decompress_into_dictionary<'a>(
        &mut self,
        ordinals: impl IntoIterator<Item = &'a BytesOrdinal>,
        decompressor_state: &mut StringDecompressorState,
        dictionary: &tantivy::columnar::Dictionary,
    ) -> std::io::Result<()> {
        let mut col = None;
        decompressor_state.rev_sorted_ords.clear();
        for o in ordinals {
            let dict = match col {
                None => {
                    if self.dictionaries.len() <= o.col as usize {
                        self.dictionaries
                            .resize_with(o.col as usize + 1, FastHashMap::default);
                    }

                    col = Some(o.col);
                    &mut self.dictionaries[col.unwrap() as usize]
                }
                Some(c) => &mut self.dictionaries[c as usize],
            };
            if !dict.contains_key(&o.ord) {
                decompressor_state.rev_sorted_ords.push(o.ord);
            }
        }

        if !decompressor_state.rev_sorted_ords.is_empty() {
            let buffers = &mut self.dictionaries[col.unwrap() as usize];
            batch_ordinals_to_buffers(
                &mut decompressor_state.rev_sorted_ords,
                &mut decompressor_state.bytes,
                buffers,
                dictionary,
            )?;
        }

        Ok(())
    }
}

pub trait ColumnarHashable {
    fn hash_key<H: Hasher>(&self, ctx: &ColumnarExprContext, state: &mut H);
}

macro_rules! default_hash {
    ($t:ty) => {
        impl ColumnarHashable for $t {
            fn hash_key<H: std::hash::Hasher>(&self, _ctx: &ColumnarExprContext, state: &mut H) {
                // bring `Hash` into scope in case the caller
                // hasn’t `use`-d it.
                ::std::hash::Hash::hash(self, state);
            }
        }
    };
}
default_hash!(bool);
default_hash!(i64);
default_hash!(u64);
default_hash!(PaginationKey);
default_hash!(tantivy::DateTime);
default_hash!(String);
default_hash!(Vec<u8>);
default_hash!(StringOrdinal);
default_hash!(BytesOrdinal);

pub trait ColumnarType:
    std::fmt::Debug + Send + Sync + PartialOrd + Clone + ColumnarHashable
{
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool;
    fn is_none(&self) -> bool {
        false
    }
}

pub trait PrimitiveColumnarType: ColumnarType {
    fn columnar_type() -> ColumnarValueType;
    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self;

    type Owned: OwnedColumnarType;
    fn to_owned_val(&self, ctx: &ColumnarExprContext) -> Self::Owned;
    fn matches_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool;
    fn lt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool;
    fn gt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool;
}

pub trait OwnedColumnarType: ColumnarType + 'static + PartialOrd {
    fn into_value(self) -> util::Value;

    fn matches(&self, value: &Self) -> bool;
}

macro_rules! default_owned {
    ($t:ty, $case:ident) => {
        impl OwnedColumnarType for $t {
            fn into_value(self) -> util::Value {
                self.into()
            }
            fn matches(&self, value: &Self) -> bool {
                self == value
            }
        }

        impl ColumnarType for $t {
            fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
                matches!(columnar_type, ColumnarValueType::$case)
            }
        }

        impl PrimitiveColumnarType for $t {
            type Owned = $t;

            fn columnar_type() -> ColumnarValueType {
                ColumnarValueType::$case
            }

            fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
                match value {
                    ColumnarValueLiteralBorrowed::$case(b) => b,
                    _ => panic!("expected {}: {value:?}", stringify!($case)),
                }
            }

            fn to_owned_val(&self, _ctx: &ColumnarExprContext) -> Self::Owned {
                *self
            }
            fn matches_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
                self == value
            }
            fn lt_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
                self < value
            }
            fn gt_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
                self > value
            }
        }
    };

    // Variant with custom pattern matching for columnar type
    ($t:ty, $case:ident, $columnar_pattern:pat) => {
        impl OwnedColumnarType for $t {
            fn into_value(self) -> util::Value {
                self.into()
            }
            fn matches(&self, value: &Self) -> bool {
                self == value
            }
        }

        impl ColumnarType for $t {
            fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
                matches!(columnar_type, $columnar_pattern)
            }
        }

        impl PrimitiveColumnarType for $t {
            type Owned = $t;

            fn columnar_type() -> ColumnarValueType {
                ColumnarValueType::$case
            }

            fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
                match value {
                    ColumnarValueLiteralBorrowed::$case(b) => b,
                    _ => panic!("expected {}: {value:?}", stringify!($case)),
                }
            }

            fn to_owned_val(&self, _ctx: &ColumnarExprContext) -> Self::Owned {
                *self
            }
            fn matches_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
                self == value
            }
            fn lt_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
                self < value
            }
            fn gt_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
                self > value
            }
        }
    };
}

default_owned!(bool, Bool);
default_owned!(i64, I64);
default_owned!(
    u64,
    U64,
    ColumnarValueType::U64 | ColumnarValueType::PaginationKey
);
default_owned!(
    PaginationKey,
    PaginationKey,
    ColumnarValueType::U64 | ColumnarValueType::PaginationKey
);
default_owned!(f64, F64);

impl ColumnarHashable for f64 {
    fn hash_key<H: Hasher>(&self, _ctx: &ColumnarExprContext, state: &mut H) {
        self.to_bits().hash(state);
    }
}

impl ColumnarType for tantivy::DateTime {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(columnar_type, ColumnarValueType::DateTime)
    }
}

impl PrimitiveColumnarType for tantivy::DateTime {
    type Owned = tantivy::DateTime;

    fn columnar_type() -> ColumnarValueType {
        ColumnarValueType::DateTime
    }

    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
        match value {
            ColumnarValueLiteralBorrowed::DateTime(i) => i,
            _ => panic!("expected DateTime: {value:?}"),
        }
    }

    fn to_owned_val(&self, _ctx: &ColumnarExprContext) -> Self::Owned {
        *self
    }
    fn matches_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self == value
    }
    fn lt_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self < value
    }
    fn gt_owned(&self, _ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self > value
    }
}

impl OwnedColumnarType for tantivy::DateTime {
    fn into_value(self) -> util::Value {
        util::Value::String(
            CastInto::<String>::cast(&self).expect("failed to cast DateTime to Value"),
        )
    }

    fn matches(&self, value: &Self) -> bool {
        self == value
    }
}

#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
pub struct BytesOrdinal {
    pub col: u32,
    pub ord: u64,
}
impl BytesOrdinal {
    pub fn new(col: u32, ord: u64) -> Self {
        Self { col, ord }
    }

    pub fn as_bytes<'a>(&self, ctx: &'a ColumnarExprContext) -> &'a [u8] {
        ctx.get_bytes_from_ordinal(self)
    }
}

#[repr(transparent)] // guarantees identical layout
#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, Hash, Default)]
pub struct StringOrdinal(BytesOrdinal);

impl StringOrdinal {
    pub fn new(col: u32, ord: u64) -> Self {
        Self(BytesOrdinal { col, ord })
    }

    pub fn as_str<'a>(&self, ctx: &'a ColumnarExprContext) -> &'a str {
        ctx.get_string_from_ordinal(self)
    }
}

impl From<&StringOrdinal> for BytesOrdinal {
    fn from(value: &StringOrdinal) -> Self {
        value.0
    }
}

impl AsRef<BytesOrdinal> for StringOrdinal {
    fn as_ref(&self) -> &BytesOrdinal {
        &self.0
    }
}

impl std::ops::Deref for StringOrdinal {
    type Target = BytesOrdinal;
    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
impl std::ops::DerefMut for StringOrdinal {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

impl ColumnarType for StringOrdinal {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(columnar_type, ColumnarValueType::StringOrdinal)
    }
}

impl ColumnarType for BytesOrdinal {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(columnar_type, ColumnarValueType::BytesOrdinal)
    }
}

impl PrimitiveColumnarType for StringOrdinal {
    type Owned = String;

    fn columnar_type() -> ColumnarValueType {
        ColumnarValueType::StringOrdinal
    }

    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
        match value {
            ColumnarValueLiteralBorrowed::StringOrdinal(i) => i,
            _ => panic!("expected StringOrdinal: {value:?}"),
        }
    }

    fn to_owned_val(&self, ctx: &ColumnarExprContext) -> Self::Owned {
        self.as_str(ctx).to_string()
    }
    fn matches_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_str(ctx) == value.as_str()
    }
    fn lt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_str(ctx) < value.as_str()
    }
    fn gt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_str(ctx) > value.as_str()
    }
}

impl PrimitiveColumnarType for BytesOrdinal {
    type Owned = Vec<u8>;

    fn columnar_type() -> ColumnarValueType {
        ColumnarValueType::BytesOrdinal
    }

    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
        match value {
            ColumnarValueLiteralBorrowed::BytesOrdinal(i) => i,
            _ => panic!("expected BytesOrdinal: {value:?}"),
        }
    }

    fn to_owned_val(&self, ctx: &ColumnarExprContext) -> Self::Owned {
        self.as_bytes(ctx).to_vec()
    }
    fn matches_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_bytes(ctx) == value.as_slice()
    }
    fn lt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_bytes(ctx) < value.as_slice()
    }
    fn gt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_bytes(ctx) > value.as_slice()
    }
}

// StringOffset is a borrowed type - it represents an offset and length into a string buffer
// managed elsewhere. It's Copy and has no lifetime issues.
#[derive(Debug, Copy, Clone, PartialEq, Eq, PartialOrd, Ord, Default)]
pub struct PtrOffset {
    // The top bit tells us whether this is a constant string or not
    pub offset: u32,
    pub length: u32,
}

impl PtrOffset {
    pub fn new(offset: usize, length: usize) -> Self {
        PtrOffset {
            offset: offset as u32,
            length: length as u32,
        }
    }

    // In a real implementation, these would look up in a string buffer
    pub fn as_str<'c>(&self, ctx: &'c ColumnarExprContext) -> &'c str {
        ctx.get_string(self)
    }
}

impl ColumnarType for PtrOffset {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(columnar_type, ColumnarValueType::StringPtr)
    }
}

impl ColumnarHashable for PtrOffset {
    #[inline]
    fn hash_key<H: Hasher>(&self, ctx: &ColumnarExprContext, state: &mut H) {
        let s = ctx.get_string(self);
        s.hash(state);
    }
}

// StringOffset is a primitive (borrowed) type - it's just an offset/length pair
impl PrimitiveColumnarType for PtrOffset {
    type Owned = String;

    fn columnar_type() -> ColumnarValueType {
        ColumnarValueType::StringPtr
    }

    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
        match value {
            ColumnarValueLiteralBorrowed::StringPtr(s) => s,
            _ => panic!("expected string: {value:?}"),
        }
    }

    fn to_owned_val(&self, ctx: &ColumnarExprContext) -> Self::Owned {
        self.as_str(ctx).to_string()
    }
    fn matches_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_str(ctx) == value.as_str()
    }
    fn lt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_str(ctx) < value.as_str()
    }
    fn gt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        self.as_str(ctx) > value.as_str()
    }
}

impl<T: ColumnarType> ColumnarType for Option<T> {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        T::matches_columnar_type(columnar_type)
    }

    fn is_none(&self) -> bool {
        self.is_none()
    }
}

impl<T: ColumnarHashable> ColumnarHashable for Option<T> {
    #[inline]
    fn hash_key<H: Hasher>(&self, ctx: &ColumnarExprContext, state: &mut H) {
        match self {
            None => {
                0u8.hash(state);
            }
            Some(v) => {
                v.hash_key(ctx, state);
            }
        }
    }
}

impl<T: PrimitiveColumnarType> PrimitiveColumnarType for Option<T> {
    type Owned = Option<T::Owned>;

    fn columnar_type() -> ColumnarValueType {
        T::columnar_type()
    }

    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
        Some(T::from_borrowed_value(value))
    }

    fn to_owned_val(&self, ctx: &ColumnarExprContext) -> Self::Owned {
        self.as_ref().map(|v| v.to_owned_val(ctx))
    }

    fn matches_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        match (self, value) {
            (None, None) => true,
            (Some(a), Some(b)) => a.matches_owned(ctx, b),
            _ => false,
        }
    }

    fn lt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        match (self, value) {
            (None, None) => false,
            (None, Some(_)) => true,
            (Some(_), None) => false,
            (Some(a), Some(b)) => a.lt_owned(ctx, b),
        }
    }
    fn gt_owned(&self, ctx: &ColumnarExprContext, value: &Self::Owned) -> bool {
        match (self, value) {
            (None, None) => false,
            (None, Some(_)) => false,
            (Some(_), None) => true,
            (Some(a), Some(b)) => a.gt_owned(ctx, b),
        }
    }
}

impl<D: OwnedColumnarType> OwnedColumnarType for Option<D> {
    fn into_value(self) -> util::Value {
        self.map(|v| v.into_value()).unwrap_or(util::Value::Null)
    }

    fn matches(&self, value: &Self) -> bool {
        match (self, value) {
            (None, None) => true,
            (Some(a), Some(b)) => a.matches(b),
            _ => false,
        }
    }
}

impl ColumnarType for String {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(
            columnar_type,
            ColumnarValueType::StringPtr | ColumnarValueType::StringOrdinal
        )
    }
}

impl OwnedColumnarType for String {
    fn into_value(self) -> util::Value {
        util::Value::String(self)
    }

    fn matches(&self, value: &Self) -> bool {
        self == value
    }
}

impl ColumnarType for Vec<u8> {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(columnar_type, ColumnarValueType::BytesOrdinal)
    }
}

impl OwnedColumnarType for Vec<u8> {
    fn into_value(self) -> util::Value {
        util::Value::String(match String::from_utf8(self) {
            Ok(s) => s,
            Err(e) => {
                let valid_up_to = e.utf8_error().valid_up_to();
                let mut bytes = e.into_bytes();
                bytes.truncate(valid_up_to);
                String::from_utf8(bytes).expect("incorrect valid_up_to")
            }
        })
    }

    fn matches(&self, value: &Self) -> bool {
        self == value
    }
}

#[derive(Debug, Clone, Copy, Eq, PartialEq, Hash, PartialOrd, Ord, Default)]
pub struct Null;

impl PrimitiveColumnarType for Null {
    type Owned = Null;

    fn columnar_type() -> ColumnarValueType {
        ColumnarValueType::Null
    }

    fn from_borrowed_value(value: ColumnarValueLiteralBorrowed) -> Self {
        match value {
            ColumnarValueLiteralBorrowed::Null => Null,
            _ => panic!("expected null: {value:?}"),
        }
    }

    fn to_owned_val(&self, _ctx: &ColumnarExprContext) -> Self::Owned {
        Null
    }
    fn matches_owned(&self, _ctx: &ColumnarExprContext, _value: &Self::Owned) -> bool {
        true
    }
    fn lt_owned(&self, _ctx: &ColumnarExprContext, _value: &Self::Owned) -> bool {
        false
    }
    fn gt_owned(&self, _ctx: &ColumnarExprContext, _value: &Self::Owned) -> bool {
        false
    }
}

impl ColumnarType for Null {
    fn matches_columnar_type(columnar_type: ColumnarValueType) -> bool {
        matches!(columnar_type, ColumnarValueType::Null)
    }
}

impl From<util::Value> for Null {
    fn from(value: util::Value) -> Self {
        match value {
            util::Value::Null => Null,
            _ => panic!("expected null: {value:?}"),
        }
    }
}

default_hash!(Null);

impl OwnedColumnarType for Null {
    fn into_value(self) -> util::Value {
        util::Value::Null
    }

    fn matches(&self, _value: &Self) -> bool {
        true
    }
}

pub fn arithmetic_value_type(left_type: ColumnarValueType, op: &ArithmeticOp) -> ColumnarValueType {
    match (left_type, op) {
        (ColumnarValueType::U64, ArithmeticOp::Sub) => ColumnarValueType::I64,
        (t, _) => t,
    }
}

// Returns the type rank for ColumnarValueType, following the same pattern as ScalarType.
// See btql/src/binder/types.rs::type_rank.
pub fn columnar_value_type_rank(t: ColumnarValueType) -> i32 {
    match t {
        ColumnarValueType::Dynamic => 0,
        ColumnarValueType::StringPtr => 1,
        ColumnarValueType::StringOrdinal => 2,
        ColumnarValueType::BytesOrdinal => 3,
        ColumnarValueType::F64 => 4,
        ColumnarValueType::I64 => 5,
        ColumnarValueType::U64 => 6,
        ColumnarValueType::Bool => 7,
        ColumnarValueType::DateTime => 8,
        ColumnarValueType::PaginationKey => 9,
        ColumnarValueType::Null => 10,
    }
}
