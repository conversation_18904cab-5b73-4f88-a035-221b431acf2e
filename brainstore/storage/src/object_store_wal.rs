use async_stream::stream;
use async_trait::async_trait;
use bytes::Bytes;
use object_store::{self, ObjectMeta, ObjectStore};
use std::collections::HashMap;
use std::path::PathBuf;
use std::sync::Arc;
use tracing::{instrument, Instrument};
use util::url_util::ObjectStoreType;

use futures::{future::join_all, stream::BoxStream, StreamExt, TryStreamExt};
use util::{
    anyhow::{self, anyhow, Context, Result},
    itertools::Itertools,
    serde_json::{self, Value},
    uuid,
    xact::TransactionId,
};

use crate::deletion_log::{DeletionLogArgs, DeletionLogStreamExt};
use crate::directory::async_directory::AsyncDirectory;
use crate::directory::AsyncDirectoryArc;
use crate::healthcheck_util::validate_object_store_connection;
use crate::paths::make_segment_directory_path;
use crate::wal::{
    DeleteFromWalStats, DeleteUpToXactIdInput, DeleteUpToXactIdOptions, WALScope, Wal,
    WalEntryVariant, WalEntryVariantPreference, WalMetadata, WalMetadataStreamOptionalInput,
};
use crate::wal_entry::WalEntry;

#[derive(Debug, Clone)]
pub struct ObjectStoreWal {
    // The store and directory should be pointing to the same filesystem location.
    pub store: Arc<dyn ObjectStore>,
    pub store_type: ObjectStoreType,
    pub directory: AsyncDirectoryArc,
    pub store_prefix: PathBuf,
}

impl ObjectStoreWal {
    pub fn wal_directory(&self, scope: WALScope) -> PathBuf {
        match scope {
            // Keep this in sync the WAL upload code in api-ts/src/brainstore/wal.ts.
            WALScope::ObjectId(object_id, wal_token) => self
                .store_prefix
                .join("object-store-objects")
                .join(wal_token.to_string())
                .join(object_id.to_string()),
            WALScope::Segment(segment_id) => {
                make_segment_directory_path(&self.store_prefix, segment_id).join("object-store-wal")
            }
        }
    }

    #[instrument(err, level = "debug", skip(directory))]
    async fn read_file(
        directory: &dyn AsyncDirectory,
        path: object_store::path::Path,
    ) -> Result<Bytes> {
        let path = PathBuf::from(path.to_string());
        let file_handle = directory.async_get_file_handle(&path, None).await?;
        let read_bytes = file_handle.async_read_bytes(0..file_handle.len()).await?;
        Ok::<_, anyhow::Error>(read_bytes.to_vec().into())
    }

    // Optionally sort a file stream alphabetically by the filename. We can optimize this away for
    // certain backends like S3, but it's necessary in general because of the lack of guarantees
    // around `ObjectStore::list`.
    // GCS also sorts by filename (https://cloud.google.com/storage/docs/listing-objects#list-objects)
    async fn sort_file_list(
        store_type: ObjectStoreType,
        stream: BoxStream<'_, Result<ObjectMeta, object_store::Error>>,
    ) -> Result<BoxStream<'_, Result<ObjectMeta, object_store::Error>>> {
        match store_type {
            ObjectStoreType::S3 | ObjectStoreType::GCS => Ok(stream),
            _ => {
                let all_values = async move {
                    let mut all_values = stream.try_collect::<Vec<_>>().await?;
                    all_values.sort_by(|a, b| a.location.cmp(&b.location));
                    Ok::<_, object_store::Error>(all_values)
                }
                .instrument(tracing::info_span!("sort WAL files"))
                .await?;
                Ok(stream! {
                    for value in all_values {
                        yield Ok(value);
                    }
                }
                .boxed())
            }
        }
    }
}

struct ObjectStoreWalMetadata {
    xact_id: TransactionId,
    object_meta: ObjectMeta,
    directory: AsyncDirectoryArc,
}

#[async_trait]
impl WalMetadata for ObjectStoreWalMetadata {
    fn xact_id(&self) -> TransactionId {
        self.xact_id
    }

    fn num_bytes(&self) -> usize {
        self.object_meta.size as usize
    }

    async fn read_wal_entries(&self, _: WalEntryVariantPreference) -> Result<Vec<WalEntryVariant>> {
        let mut entries = Vec::new();
        let bytes = ObjectStoreWal::read_file(
            self.directory.as_ref(),
            self.object_meta.location.to_owned(),
        )
        .await?;
        let deserializer = {
            let mut de = serde_json::Deserializer::from_slice(&bytes);
            // WAL entries can overflow the default serde recursion limit.
            de.disable_recursion_limit();
            de.into_iter::<Value>()
        };
        for (position_number, value) in deserializer.enumerate() {
            let entry = value
                .map_err(|e| -> anyhow::Error { e.into() })
                .and_then(WalEntry::new)
                .with_context(|| {
                    format!(
                        "Failed to parse JSON value at position {}. xact_id: {} object_location: {}",
                        position_number + 1,
                        self.xact_id,
                        self.object_meta.location
                    )
                })?;
            entries.push(WalEntryVariant::Full(entry));
        }
        Ok(entries)
    }
}

impl ObjectStoreWal {
    #[instrument(err, skip(self))]
    pub async fn wal_metadata_stream_concrete<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<ObjectStoreWalMetadata>>> {
        let wal_directory = self.wal_directory(scope);
        let store = self.store.clone();
        let store_type = self.store_type;
        let object_store_prefix = object_store::path::Path::from(wal_directory.to_str().unwrap());
        let directory = self.directory.clone();
        Ok(stream! {
              let mut file_stream = async {
                  let file_stream = match optional_input.start_xact_id {
                      Some(xact_id) => store.list_with_offset(
                          Some(&object_store_prefix),
                          &object_store_prefix.child(xact_id.0.to_string()),
                      ),
                      None => store.list(Some(&object_store_prefix)),
                  };
                  ObjectStoreWal::sort_file_list(store_type, file_stream).await
              }
              .instrument(tracing::info_span!("list WAL files"))
              .await?;

              while let Some(file) = file_stream.next().await {
                  let file = file?;
                  let object_store_filename = file.location.filename().ok_or_else(||
                      anyhow!("Invalid WAL file location: {:?}", file.location))?.parse::<ObjectStoreWalFilename>()?;
                  if let Some(end_xact_id) = optional_input.end_xact_id {
                      if object_store_filename.xact_id > end_xact_id {
                          break;
                      }
                  }
                  yield Ok(ObjectStoreWalMetadata {
                      xact_id: object_store_filename.xact_id,
                      object_meta: file,
                      directory: directory.clone(),
                  });
              }
        }.instrument(tracing::info_span!("stream WAL metadata")).boxed())
    }
}

#[async_trait]
impl Wal for ObjectStoreWal {
    #[instrument(err, skip(self, wal_entries), fields(num_entries = wal_entries.len()))]
    async fn insert<'a>(&self, scope: WALScope<'a>, wal_entries: Vec<WalEntry>) -> Result<()> {
        if wal_entries.is_empty() {
            return Ok(());
        }

        let xact_id_to_entries: HashMap<TransactionId, Vec<WalEntry>> = wal_entries
            .into_iter()
            .into_group_map_by(|entry| entry._xact_id);

        join_all(
            xact_id_to_entries
                .into_iter()
                .map(|(xact_id, entries)| async move {
                    let directory = &*self.directory;
                    let wal_filename = ObjectStoreWalFilename {
                        xact_id,
                        suffix: uuid::Uuid::new_v4().to_string(),
                    };
                    let wal_path_buf: PathBuf =
                        self.wal_directory(scope).join(wal_filename.to_string());

                    let mut bytes: Vec<u8> = Vec::new();
                    for wal_entry in entries {
                        let value_str = serde_json::to_vec(&wal_entry.to_value())?;
                        bytes.extend(value_str);
                        bytes.extend_from_slice(b"\n");
                    }
                    directory.async_atomic_write(&wal_path_buf, &bytes).await?;
                    Ok(())
                }),
        )
        .await
        .into_iter()
        .collect::<Result<()>>()?;

        Ok(())
    }

    async fn wal_metadata_stream<'a>(
        &self,
        scope: WALScope<'a>,
        optional_input: WalMetadataStreamOptionalInput,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>> {
        let concrete_stream = self
            .wal_metadata_stream_concrete(scope, optional_input)
            .await?;
        let ret = concrete_stream
            .map(|x| x.map(|x| Box::new(x) as Box<dyn WalMetadata>))
            .boxed();
        Ok(ret)
    }

    async fn status(&self) -> Result<String> {
        validate_object_store_connection(&self.store, &self.store_prefix).await?;
        Ok(format!("{:?}-type ObjectStoreWal is ok", self.store_type))
    }

    fn remove_local(&self) -> Result<()> {
        let wal_prefix_path = PathBuf::from("/").join(&self.store_prefix);
        if std::fs::metadata(&wal_prefix_path).is_ok() {
            std::fs::remove_dir_all(&wal_prefix_path)?;
        }
        Ok(())
    }

    async fn delete_up_to_xact_id<'a>(
        &self,
        input: DeleteUpToXactIdInput<'a>,
        options: &DeleteUpToXactIdOptions,
    ) -> Result<DeleteFromWalStats> {
        let wal_directory = self.wal_directory(input.scope);

        let store = self.store.clone();
        let store_type = self.store_type;
        let object_store_prefix = object_store::path::Path::from(wal_directory.to_str().unwrap());

        // List files in order of increasing xact_id.
        let file_stream = store.list(Some(&object_store_prefix));
        let file_stream = ObjectStoreWal::sort_file_list(store_type, file_stream)
            .instrument(tracing::info_span!("list WAL files"))
            .await?;

        let delete_stream = file_stream
            .map(move |file_result| {
                file_result.and_then(|file| {
                    let filename =
                        file.location
                            .filename()
                            .ok_or_else(|| object_store::Error::Generic {
                                store: "object_store_wal",
                                source: anyhow!("Invalid WAL file location: {:?}", file.location)
                                    .into(),
                            })?;

                    let object_store_filename = filename
                        .parse::<ObjectStoreWalFilename>()
                        .map_err(|e| object_store::Error::Generic {
                            store: "object_store_wal",
                            source: e.into(),
                        })?;

                    let xact_id = object_store_filename.xact_id;
                    Ok((file.location, xact_id))
                })
            })
            .try_take_while(move |(_, xact_id)| {
                futures::future::ready(Ok(*xact_id < input.min_retained_xact_id))
            })
            .map_ok(|(location, _)| PathBuf::from(location.to_string()))
            .take(options.max_num_rows as usize)
            .with_deletion_log(DeletionLogArgs {
                store: self.store.clone(),
                deletion_log_prefix: &self.store_prefix,
                dry_run: input.dry_run,
                batch_size: options.deletion_log_batch_size,
            });

        if input.dry_run {
            let planned_num_deletes = delete_stream.try_collect::<Vec<_>>().await?.len() as u64;
            tracing::info!(
                planned_num_deletes = planned_num_deletes,
                "Dry run of ObjectStoreWAL::delete_up_to_xact_id"
            );
            return Ok(DeleteFromWalStats {
                planned_num_deletes,
                num_deletes: 0,
            });
        }

        let num_deletes = self
            .directory
            .delete_stream(delete_stream)
            .try_fold(0u64, |count, _| async move { Ok(count + 1) })
            .await?;

        tracing::info!(
            num_deletes = num_deletes,
            max_num_rows = options.max_num_rows,
            "Completed ObjectStoreWAL::delete_up_to_xact_id"
        );

        Ok(DeleteFromWalStats {
            planned_num_deletes: num_deletes,
            num_deletes: num_deletes,
        })
    }
}

#[derive(Debug, Clone)]
struct ObjectStoreWalFilename {
    pub xact_id: TransactionId,
    pub suffix: String,
}

impl std::fmt::Display for ObjectStoreWalFilename {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}.{}.jsonl", self.xact_id, self.suffix)
    }
}

impl std::str::FromStr for ObjectStoreWalFilename {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> std::result::Result<Self, Self::Err> {
        let stem_s = s
            .strip_suffix(".jsonl")
            .ok_or_else(|| anyhow!("Invalid WAL path: {}", s))?;

        let mut dot_parts = stem_s.splitn(2, '.');
        let xact_id_part = dot_parts
            .next()
            .ok_or_else(|| anyhow!("Invalid WAL path: {}", s))?;
        let suffix = dot_parts
            .next()
            .ok_or_else(|| anyhow!("Invalid WAL path: {}", s))?
            .to_string();

        let xact_id = xact_id_part.parse::<TransactionId>()?;
        Ok(ObjectStoreWalFilename {
            xact_id,
            suffix: suffix.to_string(),
        })
    }
}
