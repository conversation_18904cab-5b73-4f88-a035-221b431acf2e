use futures::future::try_join_all;
use reqwest;
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, HashSet};
use util::{
    anyhow::{anyhow, Result},
    chrono::{DateTime, Utc},
    system_types::{FullObjectIdOwned, ObjectId, ObjectIdOwned},
    uuid::Uuid,
};

use crate::retention_util::RetentionObjectType;

#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct ControlPlaneContext {
    pub http_client: reqwest::Client,
    pub app_url: util::url::Url,
    pub service_token: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct RetentionPoliciesRequest {
    pub objects: Vec<RetentionObject>,
    /// For retention, we want to ignore unknown objects to avoid choking on objects
    /// that we are unauthorized to view or that don't exist at all.
    #[serde(default)]
    pub fail_unknown_objects: bool,
    /// If provided, use this to hit the control plane instead of the data plane service token
    pub service_token: Option<String>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub struct RetentionObject {
    pub object_type: RetentionObjectType,
    pub object_id: ObjectIdOwned,
}

impl std::fmt::Display for RetentionObject {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}:{}", self.object_type, self.object_id)
    }
}

#[derive(Debug, Clone, Serialize)]
pub struct RetentionPolicyConfig {
    automation_id: String,
    retention_days: i64,
}

impl RetentionPolicyConfig {
    pub fn new(automation_id: String, retention_days: i64) -> Result<Self> {
        if retention_days < 0 {
            return Err(anyhow!(
                "retention_days must be 0 or greater, got {}",
                retention_days
            ));
        }
        Ok(Self {
            automation_id,
            retention_days,
        })
    }

    pub fn automation_id(&self) -> &String {
        &self.automation_id
    }

    pub fn retention_days(&self) -> i64 {
        self.retention_days
    }
}

struct RetentionTarget {
    object_type: RetentionObjectType,
    object_id: ObjectIdOwned,
    project_id: String,
}

pub struct ResolveRetentionPoliciesInput<'a> {
    pub control_plane_ctx: &'a ControlPlaneContext,
    pub objects: Vec<RetentionObject>,
}

#[derive(Debug, Clone, Default)]
pub struct ResolveRetentionPoliciesOptionalInput {
    pub fail_on_missing_objects: bool,
    pub disable_experiments_and_datasets: bool,
}

pub async fn resolve_retention_policies(
    input: ResolveRetentionPoliciesInput<'_>,
    optional_input: ResolveRetentionPoliciesOptionalInput,
) -> Result<HashMap<FullObjectIdOwned, RetentionPolicyConfig>> {
    if input.objects.is_empty() {
        return Ok(HashMap::new());
    }

    let retention_targets = resolve_retention_targets(ResolveRetentionTargetsArgs {
        control_plane_ctx: input.control_plane_ctx,
        objects: input.objects,
        fail_on_missing_objects: optional_input.fail_on_missing_objects,
        disable_experiments_and_datasets: optional_input.disable_experiments_and_datasets,
    })
    .await?;

    let project_ids: Vec<&str> = retention_targets
        .iter()
        .map(|target| target.project_id.as_str())
        .collect::<HashSet<_>>()
        .into_iter()
        .collect();

    let project_id_to_automations = get_automations(GetAutomationsInput {
        control_plane_ctx: input.control_plane_ctx,
        project_ids: &project_ids,
    })
    .await?;

    let mut policy_map = HashMap::new();
    for target in &retention_targets {
        let automations = project_id_to_automations.get(target.project_id.as_str());
        if let Some(automations) = automations {
            let policy = resolve_policy_for_object_type(automations, &target.object_type)?;
            if let Some(policy) = policy {
                policy_map.insert(
                    FullObjectIdOwned {
                        object_type: (&target.object_type).into(),
                        object_id: target.object_id.clone(),
                    },
                    policy,
                );
            }
        }
    }

    Ok(policy_map)
}

struct ResolveRetentionTargetsArgs<'a> {
    control_plane_ctx: &'a ControlPlaneContext,
    objects: Vec<RetentionObject>,
    fail_on_missing_objects: bool,
    disable_experiments_and_datasets: bool,
}

async fn resolve_retention_targets<'a>(
    args: ResolveRetentionTargetsArgs<'a>,
) -> Result<Vec<RetentionTarget>> {
    let mut object_ids_by_type: HashMap<RetentionObjectType, Vec<ObjectIdOwned>> = HashMap::new();
    for object in args.objects {
        if args.disable_experiments_and_datasets
            && (object.object_type == RetentionObjectType::Experiment
                || object.object_type == RetentionObjectType::Dataset)
        {
            continue;
        }

        object_ids_by_type
            .entry(object.object_type)
            .or_default()
            .push(object.object_id);
    }

    let retention_targets_futures =
        object_ids_by_type
            .into_iter()
            .map(|(object_type, object_ids)| {
                resolve_retention_targets_for_object_type(
                    ResolveRetentionTargetsForObjectTypeArgs {
                        control_plane_ctx: args.control_plane_ctx,
                        object_type,
                        object_ids,
                        fail_on_missing_objects: args.fail_on_missing_objects,
                    },
                )
            });

    Ok(try_join_all(retention_targets_futures)
        .await?
        .into_iter()
        .flatten()
        .collect())
}

struct ResolveRetentionTargetsForObjectTypeArgs<'a> {
    control_plane_ctx: &'a ControlPlaneContext,
    object_type: RetentionObjectType,
    object_ids: Vec<ObjectIdOwned>,
    fail_on_missing_objects: bool,
}

async fn resolve_retention_targets_for_object_type<'a>(
    args: ResolveRetentionTargetsForObjectTypeArgs<'a>,
) -> Result<Vec<RetentionTarget>> {
    let object_ids = args
        .object_ids
        .iter()
        .map(|o| o.as_ref())
        .collect::<Vec<_>>();
    let all_object_infos = check_and_get_objects(
        CheckAndGetObjectsInput {
            control_plane_ctx: args.control_plane_ctx,
            object_type: &args.object_type,
            object_ids: &object_ids,
        },
        CheckAndGetObjectsOptionalInput {
            // Always include deleted objects for retention
            include_deleted_objects: true,
            ignore_missing_objects: !args.fail_on_missing_objects,
        },
    )
    .await?;

    // For certain object types (currently just experiments), we require objects to be deleted out
    // of the control plane before we run retention on them in brainstore.
    let object_infos: HashMap<ObjectIdOwned, ObjectInfo> = if args
        .object_type
        .retention_requires_prior_control_plane_deletion()
    {
        all_object_infos
            .into_iter()
            .filter(|(_, object_info)| object_info.is_deleted.unwrap_or(false))
            .collect()
    } else {
        all_object_infos
    };

    let mut result = Vec::new();
    for (object_id, object_info) in object_infos {
        match args.object_type {
            RetentionObjectType::ProjectLogs => {
                let project_id = object_id.to_string();
                result.push(RetentionTarget {
                    object_type: args.object_type.clone(),
                    object_id,
                    project_id,
                });
            }
            RetentionObjectType::Experiment | RetentionObjectType::Dataset => {
                let project_id = object_info
                    .parent_cols
                    .get("project")
                    .map(|p| p.id.clone())
                    .ok_or_else(|| {
                        anyhow!("Unexpected: check_and_get_objects returned no project parent for object: {}", object_id)
                    })?;
                result.push(RetentionTarget {
                    object_type: args.object_type.clone(),
                    object_id,
                    project_id,
                });
            }
        }
    }

    Ok(result)
}

fn resolve_policy_for_object_type(
    automations: &[ProjectAutomation],
    target_object_type: &RetentionObjectType,
) -> Result<Option<RetentionPolicyConfig>> {
    for a in automations {
        if let AutomationConfig::Retention {
            object_type,
            retention_days,
        } = &a.config
        {
            if object_type == target_object_type {
                let policy = RetentionPolicyConfig::new(a.id.clone(), *retention_days)?;
                return Ok(Some(policy));
            }
        }
    }
    Ok(None)
}

const SYSADMIN_ROLES: &[&str] = &["sysadmin"];

#[derive(Debug, Clone, Serialize, Deserialize, Hash, PartialEq, Eq)]
#[serde(rename_all = "snake_case")]
pub enum Permission {
    Create,
    Read,
    Update,
    Delete,
    CreateAcls,
    ReadAcls,
    UpdateAcls,
    DeleteAcls,
}

#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub struct ObjectParent {
    pub id: String,
    pub name: String,
}

#[derive(Debug, Clone, Deserialize)]
struct FetchedObject {
    pub object_id: ObjectIdOwned,

    #[serde(flatten)]
    pub object_info: ObjectInfo,
}

#[derive(Debug, Clone, Deserialize, Serialize, PartialEq, Eq)]
pub struct ObjectInfo {
    pub object_name: String,
    pub parent_cols: HashMap<String, ObjectParent>,
    pub permissions: HashSet<Permission>,
    pub is_allowed_sysadmin: Option<bool>,
    pub is_deleted: Option<bool>,
}

#[derive(Debug, Clone)]
struct CheckAndGetObjectsInput<'a> {
    pub control_plane_ctx: &'a ControlPlaneContext,
    pub object_type: &'a RetentionObjectType,
    pub object_ids: &'a [ObjectId<'a>],
}

#[derive(Debug, Clone, Default)]
struct CheckAndGetObjectsOptionalInput {
    pub include_deleted_objects: bool,
    pub ignore_missing_objects: bool,
}

async fn check_and_get_objects(
    input: CheckAndGetObjectsInput<'_>,
    optional_input: CheckAndGetObjectsOptionalInput,
) -> Result<HashMap<ObjectIdOwned, ObjectInfo>> {
    if input.object_ids.is_empty() {
        return Ok(HashMap::new());
    }

    let acl_object_type = input.object_type.acl_object_type();

    let fetched_objects = fetch_objects_from_control_plane(FetchObjectsArgs {
        control_plane_ctx: input.control_plane_ctx,
        acl_object_type,
        object_ids: &input.object_ids,
        allow_sysadmin_roles: SYSADMIN_ROLES,
        include_deleted_objects: optional_input.include_deleted_objects,
        ignore_missing_objects: optional_input.ignore_missing_objects,
    })
    .await?;

    tracing::debug!(
        "Fetched {} objects from control plane",
        fetched_objects.len()
    );

    let mut object_id_to_object_info: HashMap<ObjectIdOwned, ObjectInfo> = HashMap::new();
    for obj in fetched_objects {
        object_id_to_object_info.insert(obj.object_id, obj.object_info);
    }

    Ok(object_id_to_object_info)
}

#[derive(Debug, Clone, Serialize)]
struct GetObjectInfoRequest<'a> {
    object_type: &'a str,
    object_ids: &'a [ObjectId<'a>],
    allow_sysadmin_roles: &'a [&'a str],
    include_deleted_objects: bool,
    accept_arbitrary_acl_object_types: bool,
}

struct FetchObjectsArgs<'a> {
    control_plane_ctx: &'a ControlPlaneContext,
    acl_object_type: &'a str,
    object_ids: &'a [ObjectId<'a>],
    allow_sysadmin_roles: &'a [&'a str],
    include_deleted_objects: bool,
    ignore_missing_objects: bool,
}

async fn fetch_objects_from_control_plane(
    args: FetchObjectsArgs<'_>,
) -> Result<Vec<FetchedObject>> {
    if args.object_ids.is_empty() {
        return Ok(Vec::new());
    }

    let request_body = GetObjectInfoRequest {
        object_type: args.acl_object_type,
        object_ids: args.object_ids,
        accept_arbitrary_acl_object_types: true,
        allow_sysadmin_roles: args.allow_sysadmin_roles,
        include_deleted_objects: args.include_deleted_objects,
    };
    let response = args
        .control_plane_ctx
        .http_client
        .post(
            args.control_plane_ctx
                .app_url
                .join("api/self/get_object_info")?,
        )
        .header(
            "Authorization",
            format!("Bearer {}", args.control_plane_ctx.service_token),
        )
        .json(&request_body)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(anyhow!(
            "Failed to get object info: {}",
            response.text().await.unwrap_or_default()
        ));
    }

    let results: Vec<FetchedObject> = response.json().await?;
    if results.len() != args.object_ids.len() && !args.ignore_missing_objects {
        // TODO: Use an `HTTPError` type that encapsulates the status code and error.
        return Err(anyhow!(
            "Some objects were not found and ignore_missing_objects is false"
        ));
    }

    Ok(results)
}

#[derive(Debug, Clone, Deserialize)]
pub struct ProjectAutomation {
    pub id: String,
    pub project_id: String,
    pub user_id: Option<Uuid>,
    pub created: Option<DateTime<Utc>>,
    pub name: String,
    pub description: Option<String>,
    pub config: AutomationConfig,
}

#[derive(Debug, Clone, Deserialize)]
#[serde(tag = "event_type", rename_all = "snake_case")]
pub enum AutomationConfig {
    // Only strictly parse retention configs
    Retention {
        object_type: RetentionObjectType,
        retention_days: i64,
    },
    #[serde(other)]
    Other,
}

#[derive(Debug, Clone)]
struct GetAutomationsInput<'a> {
    pub control_plane_ctx: &'a ControlPlaneContext,
    pub project_ids: &'a [&'a str],
}

async fn get_automations<'a>(
    input: GetAutomationsInput<'a>,
) -> Result<HashMap<String, Vec<ProjectAutomation>>> {
    if input.project_ids.is_empty() {
        return Ok(HashMap::new());
    }

    let fetched_automations = fetch_automations_from_control_plane(FetchAutomationsArgs {
        control_plane_ctx: input.control_plane_ctx,
        project_ids: input.project_ids,
    })
    .await?;

    let mut project_id_to_automations: HashMap<String, Vec<ProjectAutomation>> = HashMap::new();
    for automation in fetched_automations {
        project_id_to_automations
            .entry(automation.project_id.clone())
            .or_default()
            .push(automation);
    }

    Ok(project_id_to_automations)
}

#[derive(Debug, Clone, Serialize)]
struct GetAutomationRequest<'a> {
    project_id: &'a [&'a str],
}

struct FetchAutomationsArgs<'a> {
    control_plane_ctx: &'a ControlPlaneContext,
    project_ids: &'a [&'a str],
}

async fn fetch_automations_from_control_plane(
    args: FetchAutomationsArgs<'_>,
) -> Result<Vec<ProjectAutomation>> {
    if args.project_ids.is_empty() {
        return Ok(Vec::new());
    }

    let request_body = GetAutomationRequest {
        project_id: args.project_ids,
    };

    let url = args
        .control_plane_ctx
        .app_url
        .join("api/project_automation/get")?;

    tracing::debug!(
        "Fetching automations for {} projects from API: {}",
        args.project_ids.len(),
        url
    );

    let response = args
        .control_plane_ctx
        .http_client
        .post(url)
        .header(
            "Authorization",
            format!("Bearer {}", args.control_plane_ctx.service_token),
        )
        .json(&request_body)
        .send()
        .await?;

    if !response.status().is_success() {
        // TODO: Make a dedicated `HTTPError` type that encapsulates the status code and text.
        return Err(anyhow!(
            "Failed to get project automations: {} - {}",
            response.status(),
            response.text().await.unwrap_or_default()
        ));
    }

    let all_automations: Vec<ProjectAutomation> = response.json().await?;
    Ok(all_automations)
}
