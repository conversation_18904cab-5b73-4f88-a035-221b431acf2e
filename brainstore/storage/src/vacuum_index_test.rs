use futures::TryStreamExt;
use object_store::path::Path;
use once_cell::sync::Lazy;
use serde_json::json;
use std::collections::{HashMap, HashSet};
use util::{
    anyhow::Result,
    chrono::{DateTime, Duration, Utc},
    system_types::{FullObjectId, ObjectId, ObjectType},
    test_util::assert_hashmap_eq,
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
};

use crate::{
    config_with_store::StoreInfo,
    deletion_log::{
        tests::extract_deletion_log_timestamp, DELETION_LOG_DIR, DRY_RUN_DELETION_LOG_DIR,
    },
    json_value_store::write_json_value,
    process_wal::{compact_segment_wal, process_object_wal, ProcessObjectWalOptions},
    retention_test::{run_test_with_global_stores, TestFixture},
    tantivy_footer::make_footer_fname,
    tantivy_index::{collect_meta_json, IndexMetaJson, TantivyIndexScope},
    test_util::make_compacted_wal_entries,
    vacuum::CommonVacuumOptions,
    vacuum::VacuumType,
    vacuum_index::{
        vacuum_index_stateless, VacuumIndexFullOptions, VacuumIndexInput, VacuumIndexOptionalInput,
        VacuumIndexOptions,
    },
    vacuum_test_util::{
        default_vacuum_index_full_opts_for_testing, vacuum_then_validate_index_wal,
        VacuumThenValidateIndexWalArgs,
    },
    wal::{WALScope, Wal},
    wal_entry::WalEntry,
};

fn basic_wal_entries(full_object_id: FullObjectId, segment_id: Uuid) -> Vec<WalEntry> {
    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            _is_merge: Some(true),
            id: "row0".to_string(),
            data: json!({
                "field1": format!("value0-{}", segment_id),
                "field2": 0,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: "row1".to_string(),
            data: json!({
                "field1": format!("value1-{}", segment_id),
                "field2": 1,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(2),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(3000),
            _is_merge: Some(true),
            id: "row1".to_string(),
            data: json!({
                "field1": format!("new-value1-{}", segment_id),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            _is_merge: Some(true),
            id: "row2".to_string(),
            data: json!({
                "field1": format!("value2-{}", segment_id),
                "field2": 2,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn compacted_wal_entries(full_object_id: FullObjectId, segment_id: Uuid) -> Vec<WalEntry> {
    vec![
        WalEntry {
            _pagination_key: PaginationKey(0),
            _xact_id: TransactionId(0),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(1000),
            id: "row0".to_string(),
            data: json!({
                "field1": format!("value0-{}", segment_id),
                "field2": 0,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(1),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(2000),
            id: "row1".to_string(),
            data: json!({
                "field1": format!("new-value1-{}", segment_id),
                "field2": 1,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(3),
            _xact_id: TransactionId(1),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(4000),
            id: "row2".to_string(),
            data: json!({
                "field1": format!("value2-{}", segment_id),
                "field2": 2,
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

fn more_wal_entries(full_object_id: FullObjectId, segment_id: Uuid) -> Vec<WalEntry> {
    vec![
        WalEntry {
            _pagination_key: PaginationKey(4),
            _xact_id: TransactionId(2),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(5000),
            _is_merge: Some(true),
            id: "row2".to_string(),
            data: json!({
                "field1": format!("new-value2-{}", segment_id),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
        WalEntry {
            _pagination_key: PaginationKey(5),
            _xact_id: TransactionId(2),
            _object_type: full_object_id.object_type,
            _object_id: full_object_id.object_id.to_owned(),
            created: DateTime::<Utc>::from_timestamp_nanos(6000),
            _is_merge: Some(true),
            id: "row3".to_string(),
            data: json!({
                "field1": format!("value3-{}", segment_id),
            })
            .as_object()
            .unwrap()
            .clone(),
            ..Default::default()
        },
    ]
}

async fn list_index_files(fixture: &TestFixture, segment_id: Uuid) -> HashSet<Path> {
    let index_scope = TantivyIndexScope::Segment(segment_id);
    let index_path = index_scope.path(fixture.config().index.prefix.as_path());
    let index_path_str = index_path.to_str().unwrap().to_string();
    let index_prefix = object_store::path::Path::from(index_path_str);

    let object_metas: Vec<object_store::ObjectMeta> = fixture
        .config()
        .index
        .store
        .list(Some(&index_prefix))
        .try_collect()
        .await
        .expect("Failed to list index files");

    let paths = object_metas
        .into_iter()
        .map(|file| file.location)
        .collect::<HashSet<Path>>();

    paths
}

async fn load_index_meta(fixture: &TestFixture, segment_id: Uuid) -> IndexMetaJson {
    let segment_metadata = fixture
        .config()
        .global_store
        .query_segment_metadatas(&[segment_id])
        .await
        .expect("Failed to query segment metadatas")
        .into_iter()
        .next()
        .unwrap();

    let index_meta = segment_metadata.last_compacted_index_meta;
    assert!(
        index_meta.is_some(),
        "Expected index meta to be present for segment {}",
        segment_id
    );

    let tantivy_meta = index_meta.unwrap().tantivy_meta;
    tantivy_meta
}

// NOTE(austin): This is not currently used, but it can be useful for debugging.
#[allow(dead_code)]
async fn load_local_index_meta(fixture: &TestFixture, segment_id: Uuid) -> IndexMetaJson {
    let index_store = fixture.config().clone().index;
    let index_scope = TantivyIndexScope::Segment(segment_id);
    let index_path = index_scope.path(index_store.prefix.as_path());

    let index_meta = collect_meta_json(index_store.directory.as_ref(), &index_path)
        .await
        .expect("Failed to load index meta")
        .unwrap_or_else(|| {
            panic!(
                "Expected index meta to be present for segment {}",
                segment_id
            );
        });
    index_meta
}

struct VerifyIndexFilesArgs<'a> {
    segment_id: Uuid,
    fixture: &'a TestFixture,
    #[allow(dead_code)]
    assert_no_extraneous_files: bool,
}

async fn verify_index_files<'a>(args: &VerifyIndexFilesArgs<'a>) -> HashSet<String> {
    let index_meta = load_index_meta(&args.fixture, args.segment_id).await;
    let index_files = list_index_files(&args.fixture, args.segment_id).await;

    let filenames = index_files
        .iter()
        .map(|path| path.filename().unwrap().to_string())
        .collect::<HashSet<String>>();

    assert!(
        filenames.contains(&format!("meta.json")),
        "[segment_id: {}] meta.json file is missing from listed index files",
        args.segment_id
    );
    assert!(
        filenames.len() > 1,
        "[segment_id: {}] Expected at least one file besides meta.json, but only found: {:?}",
        args.segment_id,
        filenames
    );

    let active_tantivy_segment_uuids: Vec<String> = index_meta
        .segments
        .iter()
        .map(|segment_meta| segment_meta.segment_id.uuid_string())
        .collect();

    assert!(
        active_tantivy_segment_uuids.len() > 0,
        "[segment_id: {}] Expected meta.json to contain at least one segment uuid",
        args.segment_id
    );

    let mut found_segment_uuids = HashSet::new();
    let mut extraneous_filenames = HashSet::new();
    let mut _found_footer_file = false;

    for filename in &filenames {
        if filename == "meta.json" || filename == ".managed.json" {
            continue;
        }

        if filename.ends_with(".footer") {
            if filename
                == make_footer_fname(&index_meta)
                    .expect("Failed to make footer fname")
                    .as_os_str()
                    .to_str()
                    .unwrap()
            {
                _found_footer_file = true;
            } else {
                extraneous_filenames.insert(filename.to_string());
            }

            continue;
        }

        let segment_uuid = filename.split(".").next().unwrap();
        found_segment_uuids.insert(segment_uuid.to_string());

        let is_extraneous_file = !active_tantivy_segment_uuids.contains(&segment_uuid.to_string());
        if is_extraneous_file {
            extraneous_filenames.insert(filename.to_string());
        }
    }

    // As a sanity check, assert that every segment UUID referenced in the meta.json
    // appears in at least one filename.
    for uuid in &active_tantivy_segment_uuids {
        assert!(
            found_segment_uuids.contains(uuid),
            "[segment_id: {}] Active segment UUID {} not found in any index file",
            args.segment_id,
            uuid
        );
    }

    // NOTE: This is flaky in CI. There might be an occasional mismatch between the global store
    // tantivy chunk UUID and the footer file, so vacuum cleans it up and there's no footer left.
    // Since footer files are just an optimization, let's just disable this assertion for now.
    //
    // // If there's at least one segment, assert that there's a footer file.
    // if active_tantivy_segment_uuids.len() > 0 {
    //     assert!(
    //         _found_footer_file,
    //         "[segment_id: {}] Expected footer file to be present",
    //         args.segment_id
    //     );
    // }

    // NOTE: This is flaky in CI. The background tantivy writer can write files concurrently
    // with vacuum, so we can't really assert that there are no extraneous files. Since we have
    // tests that specifically verify that vacuum cleans up both unrecognized files and index
    // files from old tantivy segments, it's safe to disable this assertion.
    // if args.assert_no_extraneous_files {
    //     assert!(
    //         extraneous_filenames.is_empty(),
    //         "[segment_id: {}] Expected no extraneous files after vacuuming but found: {:?}",
    //         args.segment_id,
    //         extraneous_filenames
    //     );
    // }

    extraneous_filenames
}

#[tokio::test]
async fn test_vacuum_index_stateless() -> Result<()> {
    run_test_with_global_stores(test_vacuum_index_stateless_inner).await
}

async fn test_vacuum_index_stateless_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let object_id = Default::default();
    let segment_id = Uuid::new_v4();
    let entries = basic_wal_entries(object_id, segment_id);

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(None),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);

    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);

    let wal: crate::object_and_global_store_wal::ObjectAndGlobalStoreWal =
        fixture.make_segment_wal();
    wal.insert(WALScope::Segment(segment_id), entries).await?;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);

    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2);
    assert_eq!(wal_entries[0].0, TransactionId(0));
    assert_eq!(wal_entries[0].1.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row0");
    assert_eq!(wal_entries[0].1[1].id, "row1");
    assert_eq!(wal_entries[1].1.len(), 2);
    assert_eq!(wal_entries[1].1[0].id, "row1");
    assert_eq!(wal_entries[1].1[1].id, "row2");

    let expected_docs = make_compacted_wal_entries(compacted_wal_entries(object_id, segment_id));
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    assert_hashmap_eq(&docs, &expected_docs);

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0, "There are no stale files yet");

    // Insert some garbage files that we expect to get deleted by vacuum.
    let garbage_uuid = Uuid::new_v4();
    let garbage_filenames = HashSet::from([
        "garbage.json".to_string(),
        format!("{}.{}.del", 100, garbage_uuid),
        format!("{}.idx", garbage_uuid),
        format!("{}.pos", garbage_uuid),
        format!("{}.{}.del", garbage_uuid, 100),
    ]);
    let index_scope = TantivyIndexScope::Segment(segment_id);
    for filename in &garbage_filenames {
        let path = index_scope
            .path(&fixture.config().index.prefix)
            .join(filename);
        write_json_value(
            fixture.config().index.directory.as_ref(),
            &path,
            serde_json::to_value("trash").as_ref().unwrap(),
        )
        .await?;
    }

    // Vacuum the real object with a 1-hour deletion grace period. Because all of the
    // files were just created, vacuuming shouldn't be allowed to delete anything.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 60 * 60, // 1 hour
                ..Default::default()
            },
            ..Default::default()
        },
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        garbage_filenames, extraneous_filenames,
        "[segment_id: {}] Expected all files to still be present after vacuuming due to grace period",
        segment_id
    );

    // Vacuum a fake object with a zero grace period.  Since it's the wrong object id,
    // the real object shouldn't get vacuumed.
    let fake_object_id = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("fake-obj").unwrap(),
    };
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[fake_object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(None),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        garbage_filenames, extraneous_filenames,
        "[segment_id: {}] Expected garbage files to still be present after vacuuming a fake object",
        segment_id
    );

    // Now run vacuum on the real object with a zero deletion grace period so that we would
    // actually vacuum the newly created index files, but do a dry run and confirm that nothing
    // gets deleted.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: true,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.planned_num_deletes, 5);
    assert_eq!(output.num_deleted_files, 0);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        garbage_filenames, extraneous_filenames,
        "[segment_id: {}] Expected garbage files to still be present after vacuuming a fake object",
        segment_id
    );

    // Now vacuum the object for real with a zero deletion grace period.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 5);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: true,
    })
    .await;
    assert!(
        extraneous_filenames.is_empty(),
        "[segment_id: {}] Expected no extraneous files for vacuumed segments",
        segment_id
    );

    // Add some new data, process and compact, then vacuum.
    let new_entries = more_wal_entries(object_id, segment_id);
    wal.insert(WALScope::Segment(segment_id), new_entries)
        .await?;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        ProcessObjectWalOptions {
            // Force a (new) segment per row.
            max_rows_per_segment: 1,
            ..Default::default()
        },
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: true,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: Some(Some(segment_id)),
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert!(
        output.num_deleted_files > 0,
        "Expected stale tantivy chunks to be deleted"
    );

    Ok(())
}

#[tokio::test]
async fn test_vacuum_index_stateless_cursor() -> Result<()> {
    let fixture = TestFixture::new(false).await;
    let wal = fixture.make_segment_wal();

    let object_id = Default::default();
    let object_ids = &[object_id];
    let mut segment_ids = vec![Uuid::new_v4(), Uuid::new_v4(), Uuid::new_v4()];
    segment_ids.sort();
    let segment_id1 = segment_ids[0];
    let segment_id2 = segment_ids[1];
    let segment_id3 = segment_ids[2];

    for segment_id in [segment_id1, segment_id2, segment_id3] {
        fixture
            .initialize_segment_metadata_in_object(object_id, segment_id)
            .await;
        wal.insert(
            WALScope::Segment(segment_id),
            basic_wal_entries(object_id, segment_id),
        )
        .await?;
    }

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    for segment_id in [segment_id1, segment_id2, segment_id3] {
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;
    }

    let input = VacuumIndexInput {
        object_ids: Some(object_ids),
        global_store: fixture.config().global_store.clone(),
        index_store: fixture.config().index.clone(),
        locks_manager: &*fixture.config().locks_manager,
        config_file_schema: Some(fixture.make_full_schema()),
        dry_run: false,
    };

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: None,
            testing_force_query_segment_metadatas_error_for_segment_id: Some(segment_id2),
            testing_force_list_error_for_segment_id: None,
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 2,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(!output.success, "Expected failure");
    assert!(output.error.is_some(), "Expected error message");
    assert_eq!(
        output.segment_id_cursor, None,
        "Expected cursor to be None since no batch was completed"
    );
    assert_eq!(
        output.num_processed_segments, 0,
        "Expected 0 processed segments"
    );

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: None,
            testing_force_query_segment_metadatas_error_for_segment_id: None,
            testing_force_list_error_for_segment_id: Some(segment_id2),
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 2,
                ..Default::default()
            },
        },
    )
    .await;

    assert!(!output.success, "Expected failure");
    assert!(output.error.is_some(), "Expected error message");
    assert_eq!(
        output.segment_id_cursor, None,
        "Expected cursor to be None since no batch was completed"
    );
    assert_eq!(
        output.num_processed_segments, 0,
        "Expected 0 processed segments"
    );

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: None,
            testing_force_query_segment_metadatas_error_for_segment_id: Some(segment_id3),
            testing_force_list_error_for_segment_id: None,
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 2,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(!output.success, "Expected failure");
    assert!(output.error.is_some(), "Expected error message");
    assert_eq!(
        output.segment_id_cursor,
        Some(segment_id2),
        "Expected cursor to be segment_id2"
    );
    assert_eq!(
        output.num_processed_segments, 2,
        "Expected 2 processed segments"
    );

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: None,
            testing_force_query_segment_metadatas_error_for_segment_id: None,
            testing_force_list_error_for_segment_id: Some(segment_id3),
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 2,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(!output.success, "Expected failure");
    assert!(output.error.is_some(), "Expected error message");
    assert_eq!(
        output.segment_id_cursor,
        Some(segment_id2),
        "Expected cursor to be segment_id2"
    );
    assert_eq!(
        output.num_processed_segments, 2,
        "Expected 2 processed segments"
    );

    // Now test supplying a cursor.
    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: Some(segment_id1),
            testing_force_query_segment_metadatas_error_for_segment_id: Some(segment_id3),
            testing_force_list_error_for_segment_id: None,
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 1,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(!output.success, "Expected failure");
    assert_eq!(
        output.segment_id_cursor,
        Some(segment_id2),
        "Expected cursor to be segment_id2"
    );
    assert_eq!(
        output.num_processed_segments, 1,
        "Expected 1 processed segments"
    );

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: Some(segment_id1),
            testing_force_query_segment_metadatas_error_for_segment_id: None,
            testing_force_list_error_for_segment_id: None,
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 5,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(output.success);
    assert!(output.error.is_none());
    assert_eq!(
        output.segment_id_cursor,
        Some(segment_id3),
        "Expected cursor to be segment_id3"
    );
    assert_eq!(
        output.num_processed_segments, 2,
        "Expected 2 processed segments"
    );

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: Some(segment_id3),
            testing_force_query_segment_metadatas_error_for_segment_id: None,
            testing_force_list_error_for_segment_id: None,
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 1,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(output.success);
    assert!(output.error.is_none());
    assert_eq!(
        output.segment_id_cursor,
        Some(segment_id3),
        "Expected cursor to be segment_id3"
    );
    assert_eq!(
        output.num_processed_segments, 0,
        "Expected 0 processed segments"
    );

    let output = vacuum_index_stateless(
        input.clone(),
        VacuumIndexOptionalInput {
            segment_id_cursor: Some(segment_id1),
            testing_force_query_segment_metadatas_error_for_segment_id: None,
            testing_force_list_error_for_segment_id: Some(segment_id1),
        },
        VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_segment_batch_size: 5,
                ..Default::default()
            },
        },
    )
    .await;
    assert!(
        output.success,
        "Expected to skip segment_id1 so we shouldn't have forced an error"
    );
    assert!(output.error.is_none());
    assert_eq!(
        output.segment_id_cursor,
        Some(segment_id3),
        "Expected cursor to be segment_id3"
    );
    assert_eq!(
        output.num_processed_segments, 2,
        "Expected 2 processed segments"
    );

    Ok(())
}

#[tokio::test]
async fn test_vacuum_index_worker() -> Result<()> {
    run_test_with_global_stores(test_vacuum_index_worker_inner).await
}

async fn test_vacuum_index_worker_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;

    let object_id = Default::default();
    let segment_id = Uuid::new_v4();
    let entries = basic_wal_entries(object_id, segment_id);

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);

    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;

    // Vacuum should now run since it hasn't run before.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);

    let wal = fixture.make_segment_wal();
    wal.insert(WALScope::Segment(segment_id), entries).await?;

    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Vacuum shouldn't run yet because the vacuum period hasn't yet elapsed.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);

    // Run compaction.
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
    assert_eq!(wal_entries.len(), 2);
    assert_eq!(wal_entries[0].0, TransactionId(0));
    assert_eq!(wal_entries[0].1.len(), 2);
    assert_eq!(wal_entries[0].1[0].id, "row0");
    assert_eq!(wal_entries[0].1[1].id, "row1");
    assert_eq!(wal_entries[1].1.len(), 2);
    assert_eq!(wal_entries[1].1[0].id, "row1");
    assert_eq!(wal_entries[1].1[1].id, "row2");

    let expected_docs = make_compacted_wal_entries(compacted_wal_entries(object_id, segment_id));
    let docs = fixture.read_segment_docs(segment_id).await;
    assert_eq!(docs.len(), 3);
    assert_hashmap_eq(&docs, &expected_docs);

    // Set last_successful_ts to 2 days ago, then check that vacuum now runs and doesn't affect the
    // data.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);

    // Do the same with empty object_ids. This should vacuum all objects.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(2),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: None,
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);

    // Try vacuuming the real object again. It should be ineligible since vacuum just ran.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);

    // Set last_successful_start_ts to a day ago and last_written_ts to 25 hours ago.
    fixture
        .config()
        .global_store
        .upsert_segment_last_written_ts(&[segment_id], Some(Utc::now() - Duration::hours(25)))
        .await?;
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;

    // Insert some garbage files that we expect to get deleted by vacuum.
    let garbage_uuid = Uuid::new_v4();
    let garbage_filenames = HashSet::from([
        "garbage.json".to_string(),
        format!("{}.{}.del", 100, garbage_uuid),
        format!("{}.idx", garbage_uuid),
        format!("{}.pos", garbage_uuid),
        format!("{}.{}.del", garbage_uuid, 100),
    ]);
    let index_scope = TantivyIndexScope::Segment(segment_id);
    for filename in &garbage_filenames {
        let path = index_scope
            .path(&fixture.config().index.prefix)
            .join(filename);
        write_json_value(
            fixture.config().index.directory.as_ref(),
            &path,
            serde_json::to_value("trash").as_ref().unwrap(),
        )
        .await?;
    }

    // If we run vacuum with a vacuum period longer than a day, nothing should happen yet.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 65 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60 + 1, // 1 day + 1 second
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);

    // Vacuum a fake object with a zero grace period. Since it's the wrong object id,
    // the real object shouldn't get vacuumed.
    let fake_object_id = FullObjectId {
        object_type: ObjectType::Experiment,
        object_id: ObjectId::new("fake-obj").unwrap(),
    };
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[fake_object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 65 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 0);
    assert_eq!(output.num_deleted_files, 0);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        garbage_filenames, extraneous_filenames,
        "[segment_id: {}] Expected garbage files to still be present after vacuuming a fake object",
        segment_id
    );

    // Now vacuum the real object with a deletion grace period over 1 hour. Vacuum should run, but
    // because all of the files were just created, it shouldn't be allowed to delete anything.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 65 * 60, // over 1 hour
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        garbage_filenames, extraneous_filenames,
        "[segment_id: {}] Expected all files to still be present after vacuuming due to grace period",
        segment_id
    );

    // Reset last_successful_start_ts to a day ago then do a dry run. Nothing should get deleted.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 61 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: false,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: true,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        garbage_filenames, extraneous_filenames,
        "[segment_id: {}] Expected all files to still be present after dry run",
        segment_id
    );

    // Now run with no grace period, but with enough slop_seconds that vacuum actually runs.
    // Vacuum should finally delete the files with a fake UUID, but not the unrecognized files.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 61 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: false,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 3);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        extraneous_filenames.len(),
        2,
        "[segment_id: {}] Expected 2 unrecognized files to still be present",
        segment_id
    );

    // Reset last_successful_start_ts to a day ago again.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;

    // With `delete_unrecognized_files` set, the unrecognized files should get wiped.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 61 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: true,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 2);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: true,
    })
    .await;
    assert!(
        extraneous_filenames.is_empty(),
        "[segment_id: {}] Expected no extraneous files for vacuumed segments",
        segment_id
    );

    // Add some new data to the WAL and re-compact.
    let new_entries = more_wal_entries(object_id, segment_id);
    wal.insert(WALScope::Segment(segment_id), new_entries)
        .await?;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        ProcessObjectWalOptions {
            // Force a (new) segment per row.
            max_rows_per_segment: 1,
            ..Default::default()
        },
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Reset last_successful_start_ts to a day ago again.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;

    // Vacuum should delete the stale tantivy chunks.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: None,
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 65 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                ..Default::default()
            },
            ..Default::default()
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert!(
        output.num_deleted_files > 0,
        "Expected stale tantivy chunks to be deleted"
    );
    verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: true,
    })
    .await;

    // Check that running again (with a specific object ID) hits the segment but doesn't
    // delete anything.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 65 * 60, // over 1 hour
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                ..Default::default()
            },
            ..Default::default()
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);
    verify_index_files(&VerifyIndexFilesArgs {
        segment_id,
        fixture: &fixture,
        assert_no_extraneous_files: true,
    })
    .await;

    Ok(())
}

static FULL_OBJECT_ID0: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj0").unwrap(),
});

static FULL_OBJECT_ID1: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj1").unwrap(),
});

static FULL_OBJECT_ID2: Lazy<FullObjectId<'static>> = Lazy::new(|| FullObjectId {
    object_type: ObjectType::Experiment,
    object_id: ObjectId::new("obj2").unwrap(),
});

#[tokio::test]
async fn test_vacuum_index_multiple_objects_memory() -> Result<()> {
    run_test_with_global_stores(test_vacuum_index_multiple_objects_inner).await
}

async fn test_vacuum_index_multiple_objects_inner(use_postgres_global_store: bool) -> Result<()> {
    let fixture = TestFixture::new(use_postgres_global_store).await;
    let vacuum_type = VacuumType::VacuumIndex;

    let segment_id0 = Uuid::new_v4();
    let segment_id1 = Uuid::new_v4();
    let segment_id2 = Uuid::new_v4();
    let segment_ids = vec![segment_id0, segment_id1, segment_id2];
    let object_ids = vec![*FULL_OBJECT_ID0, *FULL_OBJECT_ID1, *FULL_OBJECT_ID2];
    let segment_id_to_object_id = segment_ids
        .clone()
        .into_iter()
        .zip(object_ids.clone().into_iter())
        .collect::<HashMap<_, _>>();

    let mut segment_id_to_wal_entries = HashMap::new();
    for (&segment_id, &object_id) in segment_id_to_object_id.iter() {
        fixture
            .initialize_segment_metadata_in_object(object_id, segment_id)
            .await;

        let entries = basic_wal_entries(object_id, segment_id);
        segment_id_to_wal_entries.insert(segment_id, entries.clone());

        fixture
            .write_wal_to_segment(segment_id, basic_wal_entries(object_id, segment_id))
            .await;

        process_object_wal(
            fixture.basic_fixture.process_wal_input(),
            Default::default(),
            ProcessObjectWalOptions {
                max_rows_per_segment: if segment_id == segment_id0 {
                    1
                } else {
                    Default::default()
                },
                ..Default::default()
            },
        )
        .await?;
        compact_segment_wal(
            fixture.compact_wal_input(segment_id),
            Default::default(),
            Default::default(),
        )
        .await?;

        let wal_entries = fixture.read_segment_wal_entries(segment_id).await;
        assert_eq!(wal_entries.len(), 2);
        assert_eq!(wal_entries[0].1[0].id, "row0");
        assert_eq!(wal_entries[0].1[1].id, "row1");
        assert_eq!(wal_entries[1].1[1].id, "row2");

        let expected_docs =
            make_compacted_wal_entries(compacted_wal_entries(object_id, segment_id));
        let docs = fixture.read_segment_docs(segment_id).await;
        assert_eq!(docs.len(), 3);
        assert_hashmap_eq(&docs, &expected_docs);
    }

    vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&object_ids),
        stateless: false,
        options: default_vacuum_index_full_opts_for_testing(),
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;

    // Insert some stale files and random junk into the segments.
    let stale_chunk_uuid = Uuid::new_v4();
    let stale_chunk_filenames = HashSet::from([
        format!("{}.idx", stale_chunk_uuid),
        format!("{}.pos", stale_chunk_uuid),
        format!("{}.{}.del", stale_chunk_uuid, 100),
    ]);
    let unrecognized_filenames = HashSet::from([
        "garbage.json".to_string(),
        format!("{}.{}.del", 100, stale_chunk_uuid),
    ]);

    for segment_id in &segment_ids {
        let index_scope = TantivyIndexScope::Segment(*segment_id);
        for filename in stale_chunk_filenames
            .iter()
            .chain(unrecognized_filenames.iter())
        {
            let path = index_scope
                .path(&fixture.config().index.prefix)
                .join(filename);
            write_json_value(
                fixture.config().index.directory.as_ref(),
                &path,
                serde_json::to_value("trash").as_ref().unwrap(),
            )
            .await?;
        }
    }

    // Set last successful vacuum start_ts to a day ago for segment_id1 and segment_id2.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id1, segment_id2],
            vacuum_type,
            Utc::now() - Duration::days(1),
        )
        .await?;

    // Vacuum object_id0 and object_id1. Note that segment_id0 shouldn't get vacuumed
    // because its last vacuum run isn't stale enough yet. Vacuum should run for segment_id1
    // but not delete the unrecognized files since `vacuum_index_delete_unrecognized_files` is false.
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 23 * 60 * 60,
                ..Default::default()
            },
            ..Default::default()
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_deleted_files, 3);
    assert_eq!(output.num_processed_segments, 1);
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id: segment_id0,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        stale_chunk_filenames.iter().chain(unrecognized_filenames.iter())
            .map(|s| s.clone())
            .collect::<HashSet<_>>(),
        extraneous_filenames,
        "[segment_id: {}] Expected stale chunk files and unrecognized files to be present for unvacuumed segment",
        segment_id0
    );
    let extraneous_filenames = verify_index_files(&VerifyIndexFilesArgs {
        segment_id: segment_id1,
        fixture: &fixture,
        assert_no_extraneous_files: false,
    })
    .await;
    assert_eq!(
        unrecognized_filenames, extraneous_filenames,
        "[segment_id: {}] Expected only unrecognized files to be present for vacuumed segment",
        segment_id1
    );

    // Vacuum again for segment_id1 with `vacuum_index_delete_unrecognized_files` set.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id1],
            vacuum_type,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[*FULL_OBJECT_ID0, *FULL_OBJECT_ID1]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 23 * 60 * 60,
                vacuum_index_delete_unrecognized_files: true,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_deleted_files, 2);
    assert_eq!(output.num_processed_segments, 1);
    verify_index_files(&VerifyIndexFilesArgs {
        segment_id: segment_id1,
        fixture: &fixture,
        assert_no_extraneous_files: true,
    })
    .await;

    // Now set the last successful start_ts for segment_id0. Only segment_id0 and segment_id2
    // should get vacuumed, since segment_id1 got vacuumed already. All of the extraneous files
    // should be gone since we set `vacuum_index_delete_unrecognized_files` to true.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id0],
            vacuum_type,
            Utc::now() - Duration::days(1),
        )
        .await?;

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&object_ids),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 23 * 60 * 60,
                vacuum_index_delete_unrecognized_files: true,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_deleted_files, 5 * 2); // 5 files * 2 segments
    assert_eq!(output.num_processed_segments, 2);
    for &segment_id in &segment_ids {
        verify_index_files(&VerifyIndexFilesArgs {
            segment_id,
            fixture: &fixture,
            assert_no_extraneous_files: segment_id != segment_id1,
        })
        .await;
    }

    // Set the last successful start_ts for segment_id1 to a day ago, then insert new WAL entries
    // and re-compact. Vacuum should also wipe the original unrecognized file.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id1],
            vacuum_type,
            Utc::now() - Duration::days(1),
        )
        .await?;

    let new_entries = more_wal_entries(*FULL_OBJECT_ID1, segment_id1);
    fixture
        .write_wal_to_segment(segment_id1, new_entries.clone())
        .await;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        ProcessObjectWalOptions {
            max_rows_per_segment: 1,
            ..Default::default()
        },
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id1),
        Default::default(),
        Default::default(),
    )
    .await?;

    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&object_ids),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: false,
                ..Default::default()
            },
            ..Default::default()
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);
    assert!(
        output.num_deleted_files > 0,
        "Expected stale tantivy chunks to be deleted"
    );
    verify_index_files(&VerifyIndexFilesArgs {
        segment_id: segment_id1,
        fixture: &fixture,
        assert_no_extraneous_files: true,
    })
    .await;

    Ok(())
}

async fn read_deletion_log_paths(
    index_store: &StoreInfo,
    deletion_log_path: &object_store::path::Path,
) -> Result<Vec<String>> {
    let store = index_store.store.clone();
    let file_content = store.get(deletion_log_path).await?.bytes().await?;
    let content_str = String::from_utf8_lossy(&file_content);
    let paths = content_str.lines().map(|line| line.to_string()).collect();
    Ok(paths)
}

#[tokio::test]
async fn test_vacuum_index_deletion_log() -> Result<()> {
    let fixture = TestFixture::new(false).await;

    let object_id = Default::default();
    let segment_id = Uuid::new_v4();
    fixture
        .initialize_segment_metadata_in_object(object_id, segment_id)
        .await;
    let wal = fixture.make_segment_wal();
    let entries = basic_wal_entries(object_id, segment_id);
    wal.insert(WALScope::Segment(segment_id), entries).await?;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    // Insert extraneous files that will be deleted during vacuum.
    let junk_uuid = Uuid::new_v4();
    let extraneous_filenames = HashSet::from([
        "foo.json".to_string(),
        format!("{}.{}.del", 100, junk_uuid),
        format!("{}.idx", junk_uuid),
        format!("{}.pos", junk_uuid),
        format!("{}.{}.del", junk_uuid, 100),
    ]);
    let mut extraneous_paths = HashSet::new();

    let index_scope = TantivyIndexScope::Segment(segment_id);
    for filename in &extraneous_filenames {
        let path = index_scope
            .path(&fixture.config().index.prefix)
            .join(filename);
        extraneous_paths.insert(path.to_str().unwrap().to_string());
        write_json_value(
            fixture.config().index.directory.as_ref(),
            &path,
            serde_json::to_value("zzz").as_ref().unwrap(),
        )
        .await?;
    }

    // Record all the files in the index directory before vacuum.
    let index_files_before = list_index_files(&fixture, segment_id).await;
    let paths_before = index_files_before
        .iter()
        .map(|path| path.to_string())
        .collect::<HashSet<String>>();

    // Do a dry run. Nothing should get deleted, but the planned deletions should get logged.
    let before_ts = Utc::now().timestamp() as u64;
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                vacuum_deletion_log_batch_size: 3,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: true,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: true,
    })
    .await;
    let after_ts = Utc::now().timestamp() as u64;

    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, 0);
    let index_store = fixture.config().index.clone();
    let dry_run_deletion_log_path = index_store.prefix.join(DRY_RUN_DELETION_LOG_DIR);
    let dry_run_deletion_log_path_str = dry_run_deletion_log_path
        .to_str()
        .ok_or("Failed to convert prefix to string")
        .unwrap();
    let dry_run_deletion_log_prefix = object_store::path::Path::from(dry_run_deletion_log_path_str);

    // Since we ran with a batch size of 3, the first file should contain 3 paths
    // and the second should contain the remaining 2.
    let dry_run_deletion_log_files = index_store
        .store
        .list(Some(&dry_run_deletion_log_prefix))
        .try_collect::<Vec<_>>()
        .await?;
    assert!(
        !dry_run_deletion_log_files.is_empty(),
        "Expected at least one dry run deletion_log file"
    );
    assert_eq!(dry_run_deletion_log_files.len(), 2);

    // Read the contents of the dry_run_deletion_log files and check that they contain the deleted paths.
    let mut logged_deleted_paths = HashSet::new();
    for deletion_log_file in dry_run_deletion_log_files.iter() {
        let file_paths = read_deletion_log_paths(&index_store, &deletion_log_file.location).await?;
        let num_paths = file_paths.len();
        assert!(
            num_paths == 2 || num_paths == 3,
            "Expected file to have 2 or 3 paths, got {}",
            num_paths
        );
        for path in file_paths {
            logged_deleted_paths.insert(path.to_string());
        }

        // Check that the deletion log timestamp is between before_ts and after_ts.
        let deletion_log_timestamp = extract_deletion_log_timestamp(
            &deletion_log_file.location.filename().unwrap().to_string(),
        )?;
        assert!(
            deletion_log_timestamp >= before_ts && deletion_log_timestamp <= after_ts,
            "Deletion log timestamp is not between {} and {}",
            before_ts,
            after_ts
        );
    }
    assert_eq!(logged_deleted_paths.len(), 5);

    // Now force vacuum to run to run for real.
    let before_ts = Utc::now().timestamp() as u64;
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                vacuum_deletion_log_batch_size: 3,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: true,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    let after_ts = Utc::now().timestamp() as u64;

    assert_eq!(output.num_processed_segments, 1);
    assert_eq!(output.num_deleted_files, extraneous_filenames.len());

    let index_files_after = list_index_files(&fixture, segment_id).await;
    let paths_after = index_files_after
        .iter()
        .map(|path| path.to_string())
        .collect::<HashSet<String>>();
    let deleted_paths = paths_before
        .difference(&paths_after)
        .cloned()
        .collect::<HashSet<String>>();
    for extraneous_path in &extraneous_paths {
        assert!(
            deleted_paths.contains(extraneous_path),
            "Expected extraneous file {} to be deleted",
            extraneous_path
        );
    }

    let index_store = fixture.config().index.clone();
    let deletion_log_path = index_store.prefix.join(DELETION_LOG_DIR);
    let deletion_log_path_str = deletion_log_path
        .to_str()
        .ok_or("Failed to convert prefix to string")
        .unwrap();
    let deletion_log_prefix = object_store::path::Path::from(deletion_log_path_str);

    // Now check the deletion logs for records of the deleted files. Since we ran with a batch size
    // of 3, the first log should contain 3 paths and the second log should contain the remaining 2.
    let deletion_log_files = index_store
        .store
        .list(Some(&deletion_log_prefix))
        .try_collect::<Vec<_>>()
        .await?;
    assert!(
        !deletion_log_files.is_empty(),
        "Expected at least one deletion log file"
    );
    assert_eq!(deletion_log_files.len(), 2);

    // Read the contents of the deletion logs and check that they contain the deleted paths.
    let mut logged_deleted_paths = HashSet::new();
    for deletion_log_file in deletion_log_files.iter() {
        let file_paths = read_deletion_log_paths(&index_store, &deletion_log_file.location).await?;
        let num_paths = file_paths.len();
        assert!(
            num_paths == 2 || num_paths == 3,
            "Expected deletion log to have 2 or 3 paths, got {}",
            num_paths
        );
        for path in file_paths {
            logged_deleted_paths.insert(path.to_string());
        }

        // Check that the deletion log timestamp is between before_ts and after_ts.
        let deletion_log_timestamp = extract_deletion_log_timestamp(
            &deletion_log_file.location.filename().unwrap().to_string(),
        )?;
        assert!(
            deletion_log_timestamp >= before_ts && deletion_log_timestamp <= after_ts,
            "Deletion log timestamp is not between {} and {}",
            before_ts,
            after_ts
        );
    }
    assert_eq!(logged_deleted_paths.len(), 5);

    // Verify that all deleted files were recorded in deletion logs.
    for extraneous_path in &extraneous_paths {
        assert!(
            logged_deleted_paths.contains(extraneous_path),
            "Expected extraneous file {} to be deleted",
            extraneous_path
        );
    }

    // Now insert some new rows, run recompaction, and verify that some old files were
    // deleted and appear as a new entry in the deletion log.
    let new_entries = more_wal_entries(object_id, segment_id);
    fixture
        .write_wal_to_segment(segment_id, new_entries.clone())
        .await;
    process_object_wal(
        fixture.basic_fixture.process_wal_input(),
        Default::default(),
        Default::default(),
    )
    .await?;
    compact_segment_wal(
        fixture.compact_wal_input(segment_id),
        Default::default(),
        Default::default(),
    )
    .await?;

    let before_ts = Utc::now().timestamp() as u64;

    // Force vacuum to run with a large batch size.
    fixture
        .config()
        .global_store
        .upsert_segment_vacuum_last_successful_start_ts(
            &[segment_id],
            VacuumType::VacuumIndex,
            Utc::now() - Duration::days(1),
        )
        .await?;
    let output = vacuum_then_validate_index_wal(VacuumThenValidateIndexWalArgs {
        config_with_store: &fixture.config(),
        full_schema: fixture.make_full_schema(),
        object_ids: Some(&[object_id]),
        stateless: false,
        options: VacuumIndexFullOptions {
            common_vacuum_opts: CommonVacuumOptions {
                vacuum_deletion_grace_period_seconds: 0,
                vacuum_last_written_slop_seconds: 0,
                vacuum_deletion_log_batch_size: 1000,
                ..Default::default()
            },
            vacuum_index_opts: VacuumIndexOptions {
                vacuum_index_period_seconds: 24 * 60 * 60, // 1 day
                vacuum_index_delete_unrecognized_files: false,
                ..Default::default()
            },
        },
        expected_segment_id_cursor: None,
        dry_run: false,
    })
    .await;
    assert_eq!(output.num_processed_segments, 1);

    let after_ts = Utc::now().timestamp() as u64;

    let num_deleted_files = output.num_deleted_files;
    assert!(num_deleted_files > 0, "Expected some files to be deleted");

    // Check that there is one new deletion log file (for a total of 3). The new file should contain
    // all of the file paths that were deleted because we ran with a huge deletion log batch size.
    let deletion_log_files = index_store
        .store
        .list(Some(&deletion_log_prefix))
        .try_collect::<Vec<_>>()
        .await?;
    assert_eq!(deletion_log_files.len(), 3);
    let new_deletion_log_file = deletion_log_files
        .iter()
        .max_by_key(|meta| meta.last_modified)
        .unwrap();

    // Check that the deletion log timestamp is between before_ts and after_ts.
    let deletion_log_timestamp = extract_deletion_log_timestamp(
        &new_deletion_log_file
            .location
            .filename()
            .unwrap()
            .to_string(),
    )?;
    assert!(
        deletion_log_timestamp >= before_ts && deletion_log_timestamp <= after_ts,
        "Deletion log timestamp is not between {} and {}",
        before_ts,
        after_ts
    );

    let new_deletion_log_file_paths =
        read_deletion_log_paths(&index_store, &new_deletion_log_file.location).await?;
    assert_eq!(new_deletion_log_file_paths.len(), num_deleted_files);

    Ok(())
}
