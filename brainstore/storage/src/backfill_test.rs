//! Tests for the backfill functionality.
//!
//! This test suite verifies that the `backfill_one_tracking_entry` function correctly:
//! 1. Processes tracking entries and their associated brainstore objects
//! 2. Handles batch processing with configurable batch sizes
//! 3. Updates tracking entries with the correct sequence IDs after processing
//! 4. Respects locking to prevent concurrent processing
//! 5. Processes both regular logs and logs2 entries

use std::sync::{atomic::AtomicBool, Arc};

use util::{
    anyhow::Result,
    chrono::Utc,
    system_types::{FullObjectId, FullObjectIdOwned, ObjectId, ObjectIdOwned, ObjectType},
    xact::TransactionId,
};

use crate::{
    backfill::{
        backfill_one_tracking_entry, backfill_worker, BackfillOneTrackingEntryInput,
        BackfillOneTrackingEntryOptionalInput, BackfillOneTrackingEntryOptions,
        BackfillWorkerInput, BackfillWorkerOptionalInput, BackfillWorkerOptions,
    },
    compaction_loop::CompactionLoop,
    config_with_store::{url_to_global_store, ConfigWithStore},
    directory::cached_directory::FileCacheOpts,
    global_store::{BackfillTrackingEntry, TestingOnlyBackfillBrainstoreObjectAtom},
    global_store_test::{get_postgres_global_store_migration, POSTGRES_EXTERNAL_MIGRATION},
    object_and_global_store_wal::ObjectAndGlobalStoreWal,
    process_wal::ProcessObjectWalOptions,
    test_util::{collect_wal_stream, PostgresContainer, TmpDirConfigWithStore},
    wal::{wal_stream, WALScope, Wal},
    wal_entry::WalEntry,
    wal_test_util::insert_object_atoms_into_wal,
};

async fn stream_object_wal(
    object_id: FullObjectId<'_>,
    config: &ConfigWithStore,
) -> Result<Vec<WalEntry>> {
    let segment_ids = config
        .global_store
        .list_segment_ids(&[object_id], None)
        .await?
        .remove(0);
    let mut wal_entries = Vec::new();
    for segment_id in segment_ids {
        let segment_wal = ObjectAndGlobalStoreWal {
            object_store: config.index.store.clone(),
            global_store: config.global_store.clone(),
            directory: config.index.directory.clone(),
            store_prefix: config.index.prefix.clone(),
            store_type: config.index.store_type,
        };
        let collected_entries = collect_wal_stream(wal_stream(
            segment_wal
                .wal_metadata_stream(WALScope::Segment(segment_id), Default::default())
                .await?,
            Default::default(),
        ))
        .await?;
        wal_entries.extend(
            collected_entries
                .into_iter()
                .flat_map(|(_, entries)| entries),
        );
    }
    wal_entries.sort_by_key(|entry| (entry._xact_id, entry.id.clone()));
    Ok(wal_entries)
}

/// Helper function to test backfill_one_tracking_entry with configurable global store
async fn test_backfill_one_tracking_entry_helper(use_postgres_global_store: bool) -> Result<()> {
    let mut tmp_dir_config = TmpDirConfigWithStore::new();
    let wal = tmp_dir_config.config.wal.clone();

    // Set up postgres global store if requested
    let _container = if use_postgres_global_store {
        let container = PostgresContainer::new().await;
        container
            .run_migration(POSTGRES_EXTERNAL_MIGRATION)
            .await
            .unwrap();
        container
            .run_migration(&get_postgres_global_store_migration())
            .await
            .unwrap();
        Some(container)
    } else {
        None
    };

    if use_postgres_global_store {
        let global_store = url_to_global_store(
            &_container.as_ref().unwrap().connection_url,
            FileCacheOpts::default(),
            tmp_dir_config.config.locks_manager.clone(),
        )
        .unwrap();
        tmp_dir_config.config.global_store = global_store.0;
    }

    let global_store = tmp_dir_config.config.global_store.clone();
    let compaction_loop = CompactionLoop::default(); // This is not really used

    // Create test data with spaced-out sequence IDs to test batching
    let sequence_id_batch_size = 100;
    let mut tracking_entries: Vec<BackfillTrackingEntry> = Vec::new();
    for object_type in [
        ObjectType::Experiment,
        ObjectType::Dataset,
        ObjectType::PlaygroundLogs,
    ] {
        tracking_entries.push(BackfillTrackingEntry {
            project_id: "test_project".to_string(),
            object_type,
            last_processed_sequence_id: 0,
            last_encountered_sequence_id: 350, // This will require multiple batches
            last_processed_sequence_id_2: 0,
            last_encountered_sequence_id_2: 250,
            completed_initial_backfill_ts: Some(Utc::now()),
        });
    }

    let brainstore_objects = vec![
        // First batch of objects (sequence IDs 0-100)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 50,
            xact_id: TransactionId(101),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 90,
            xact_id: TransactionId(102),
        },
        // Second batch of objects (sequence IDs 100-200)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Dataset,
                object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 150,
            xact_id: TransactionId(103),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Dataset,
                object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 180,
            xact_id: TransactionId(104),
        },
        // Third batch of objects (sequence IDs 200-300)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::PlaygroundLogs,
                object_id: ObjectIdOwned::new("obj3".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 250,
            xact_id: TransactionId(105),
        },
        // Fourth batch of objects (sequence IDs 300-400)
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::PlaygroundLogs,
                object_id: ObjectIdOwned::new("obj4".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 320,
            xact_id: TransactionId(106),
        },
        // logs2 objects for testing logs2 processing
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj5".to_string()).unwrap(),
            },
            is_logs2: true,
            sequence_id: 100,
            xact_id: TransactionId(107),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj5".to_string()).unwrap(),
            },
            is_logs2: true,
            // Use a much larger sequence ID to test that we artificially extend
            // the sequence ID of rows we search for to grab all rows in the
            // transaction. This also requires using the same transaction ID as
            // the previous entry.
            sequence_id: 100 + 10000,
            xact_id: TransactionId(107),
        },
    ];

    // Insert test data
    insert_object_atoms_into_wal(
        brainstore_objects,
        tracking_entries.clone(),
        wal.clone(),
        global_store.clone(),
    )
    .await?;

    // Test the backfill function
    for tracking_entry in &tracking_entries {
        let options = BackfillOneTrackingEntryOptions {
            sequence_id_batch_size,
            ..Default::default()
        };

        let input = BackfillOneTrackingEntryInput {
            tracking_entry: tracking_entry.clone(),
            config: &tmp_dir_config.config,
            process_wal_opts: &ProcessObjectWalOptions::default(),
            compaction_loop: &compaction_loop,
        };

        let output = backfill_one_tracking_entry(
            input,
            BackfillOneTrackingEntryOptionalInput::default(),
            options,
        )
        .await?;

        // Check the output fields directly
        assert!(output.acquired_lock, "Should have acquired the lock");
        assert_eq!(
            output.num_iterations, 7,
            "Should have performed exactly 7 iterations: 4 for regular logs (0->350 in batches of 100) + 3 for logs2 (0->250 in batches of 100)"
        );

        // Verify that the tracking entry was updated correctly
        let updated_entry = global_store
            .query_backfill_tracking_entries_by_ids(&[tracking_entry.id()])
            .await?
            .remove(0)
            .unwrap();
        assert_eq!(
            updated_entry.last_processed_sequence_id, 350,
            "Regular logs should be processed up to sequence ID 350"
        );
        assert_eq!(
            updated_entry.last_processed_sequence_id_2, 250,
            "Logs2 should be processed up to sequence ID 250"
        );
    }

    // Check that objects were processed by verifying their WAL entries exist
    // Expected WAL entries per object based on the test data:
    // - obj1: 2 entries (sequence IDs 50, 90)
    // - obj2: 2 entries (sequence IDs 150, 180)
    // - obj3: 1 entry (sequence ID 250)
    // - obj4: 1 entry (sequence ID 320)
    // - obj5: 2 entries (sequence IDs 100, 200 - logs2)
    let expected_wal_counts = vec![
        (
            FullObjectId {
                object_type: ObjectType::Experiment,
                object_id: ObjectId::new("obj1").unwrap(),
            },
            2,
        ),
        (
            FullObjectId {
                object_type: ObjectType::Dataset,
                object_id: ObjectId::new("obj2").unwrap(),
            },
            2,
        ),
        (
            FullObjectId {
                object_type: ObjectType::PlaygroundLogs,
                object_id: ObjectId::new("obj3").unwrap(),
            },
            1,
        ),
        (
            FullObjectId {
                object_type: ObjectType::PlaygroundLogs,
                object_id: ObjectId::new("obj4").unwrap(),
            },
            1,
        ),
        (
            FullObjectId {
                object_type: ObjectType::Experiment,
                object_id: ObjectId::new("obj5").unwrap(),
            },
            2,
        ),
    ];

    for (full_object_id, expected_count) in expected_wal_counts {
        let wal_entries = stream_object_wal(full_object_id, &tmp_dir_config.config).await?;
        assert_eq!(
            wal_entries.len(),
            expected_count,
            "Object {:?} should have exactly {} WAL entries after processing",
            full_object_id,
            expected_count
        );
    }

    Ok(())
}

/// Tests that `backfill_one_tracking_entry` correctly processes tracking entries
/// and their associated brainstore objects, handling both regular logs and logs2
/// with appropriate batch processing using the default global store.
#[tokio::test]
async fn test_backfill_one_tracking_entry() -> Result<()> {
    test_backfill_one_tracking_entry_helper(false).await
}

/// Tests that `backfill_one_tracking_entry` correctly processes tracking entries
/// and their associated brainstore objects, handling both regular logs and logs2
/// with appropriate batch processing using a postgres global store.
#[tokio::test]
async fn test_backfill_one_tracking_entry_postgres() -> Result<()> {
    test_backfill_one_tracking_entry_helper(true).await
}

/// Tests that `backfill_one_tracking_entry` correctly handles the case where
/// it cannot acquire the required lock (i.e., another process is already
/// working on the same tracking entry).
#[tokio::test]
async fn test_backfill_one_tracking_entry_no_lock() -> Result<()> {
    let tmp_dir_config = TmpDirConfigWithStore::new();
    let global_store = tmp_dir_config.config.global_store.clone();
    let locks_manager = tmp_dir_config.config.locks_manager.clone();
    let compaction_loop = CompactionLoop::default();

    let tracking_entry = BackfillTrackingEntry {
        project_id: "test_project".to_string(),
        object_type: ObjectType::Experiment,
        last_processed_sequence_id: 0,
        last_encountered_sequence_id: 100,
        last_processed_sequence_id_2: 0,
        last_encountered_sequence_id_2: 100,
        completed_initial_backfill_ts: Some(Utc::now()),
    };

    // Insert the tracking entry first
    global_store
        .testing_only_insert_backfill_data(vec![tracking_entry.clone()], vec![])
        .await?;

    // Acquire the lock first so the backfill function can't get it
    let _lock = locks_manager
        .write(&format!(
            "backfill_one_tracking_entry:{}",
            tracking_entry.id()
        ))
        .await?;

    let input = BackfillOneTrackingEntryInput {
        tracking_entry: tracking_entry.clone(),
        config: &tmp_dir_config.config,
        process_wal_opts: &ProcessObjectWalOptions::default(),
        compaction_loop: &compaction_loop,
    };

    let output = backfill_one_tracking_entry(
        input,
        BackfillOneTrackingEntryOptionalInput::default(),
        BackfillOneTrackingEntryOptions::default(),
    )
    .await?;

    // Check the output fields directly
    assert!(!output.acquired_lock, "Should not have acquired the lock");
    assert_eq!(
        output.num_iterations, 0,
        "Should have performed zero iterations when lock not acquired - no processing should occur"
    );

    // Also verify by checking that the tracking entry was NOT updated (no processing occurred)
    let entries = global_store
        .query_unbackfilled_tracking_entries_ordered(true, None, 10)
        .await?;

    if !entries.is_empty() {
        let entry = &entries[0];
        // Should still be at the original values since lock couldn't be acquired
        assert_eq!(entry.last_processed_sequence_id, 0);
        assert_eq!(entry.last_processed_sequence_id_2, 0);
        // Also check that encountered values are unchanged
        assert_eq!(entry.last_encountered_sequence_id, 100);
        assert_eq!(entry.last_encountered_sequence_id_2, 100);
    }

    Ok(())
}

/// Tests that `backfill_one_tracking_entry` correctly handles processing
/// with a small batch size, ensuring that it can handle multiple iterations
/// to process all the data.
#[tokio::test]
async fn test_backfill_one_tracking_entry_with_small_batch() -> Result<()> {
    let tmp_dir_config = TmpDirConfigWithStore::new();
    let wal = tmp_dir_config.config.wal.clone();
    let global_store = tmp_dir_config.config.global_store.clone();

    let tracking_entry = BackfillTrackingEntry {
        project_id: "test_project".to_string(),
        object_type: ObjectType::Experiment,
        last_processed_sequence_id: 0,
        last_encountered_sequence_id: 200,
        last_processed_sequence_id_2: 0,
        last_encountered_sequence_id_2: 0,
        completed_initial_backfill_ts: Some(Utc::now()),
    };

    // Create objects that will require multiple small batches
    let brainstore_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 10,
            xact_id: TransactionId(201),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj2".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 60,
            xact_id: TransactionId(202),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj3".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 110,
            xact_id: TransactionId(203),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("obj4".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 160,
            xact_id: TransactionId(204),
        },
    ];

    insert_object_atoms_into_wal(
        brainstore_objects,
        vec![tracking_entry.clone()],
        wal.clone(),
        global_store.clone(),
    )
    .await?;

    // Use a very small batch size to force more iterations
    let options = BackfillOneTrackingEntryOptions {
        sequence_id_batch_size: 50, // Small batch size
        max_backfill_iterations: 1000,
    };

    let compaction_loop = CompactionLoop::default();
    let input = BackfillOneTrackingEntryInput {
        tracking_entry: tracking_entry.clone(),
        config: &tmp_dir_config.config,
        process_wal_opts: &ProcessObjectWalOptions::default(),
        compaction_loop: &compaction_loop,
    };

    let output = backfill_one_tracking_entry(
        input,
        BackfillOneTrackingEntryOptionalInput::default(),
        options,
    )
    .await?;

    // Check the output fields directly
    assert!(output.acquired_lock, "Should have acquired the lock");
    assert_eq!(
        output.num_iterations, 4,
        "Should have performed exactly 4 iterations: 4 for regular logs (0->200 in batches of 50), no logs2 processing needed"
    );

    // Verify that processing completed despite small batch size
    let updated_entries = global_store
        .query_backfill_tracking_entries_by_ids(&[tracking_entry.id()])
        .await?;
    assert_eq!(updated_entries.len(), 1);

    let updated_entry = updated_entries[0].as_ref().unwrap();
    assert_eq!(
        updated_entry.last_processed_sequence_id, 200,
        "Should process all sequence IDs even with small batch size"
    );

    // Check that objects were processed by verifying their WAL entries exist
    // Expected WAL entries per object based on the test data:
    // - obj1: 1 entry (sequence ID 10)
    // - obj2: 1 entry (sequence ID 60)
    // - obj3: 1 entry (sequence ID 110)
    // - obj4: 1 entry (sequence ID 160)
    let expected_wal_counts = vec![
        (ObjectId::new("obj1").unwrap(), 1),
        (ObjectId::new("obj2").unwrap(), 1),
        (ObjectId::new("obj3").unwrap(), 1),
        (ObjectId::new("obj4").unwrap(), 1),
    ];

    for (object_id, expected_count) in expected_wal_counts {
        let full_object_id = FullObjectId {
            object_type: ObjectType::Experiment,
            object_id,
        };
        let wal_entries = stream_object_wal(full_object_id, &tmp_dir_config.config).await?;
        assert_eq!(
            wal_entries.len(),
            expected_count,
            "Object {:?} should have exactly {} WAL entries after processing",
            object_id,
            expected_count
        );
    }

    Ok(())
}

/// Tests that the `backfill_worker` correctly processes un-backfilled tracking
/// entries. This is a helper function parameterized over whether or not we are
/// working on backfilled or un-backfilled entries.
///
/// This test verifies that the backfill worker:
/// 1. Continuously polls for un-backfilled tracking entries
/// 2. Processes them using the configured number of worker tasks
/// 3. Updates tracking entries with correct sequence IDs after processing
/// 4. Terminates gracefully when the terminate signal is set
/// 5. Processes both regular logs and logs2 entries
/// 6. Creates WAL entries for all processed objects
///
/// The test flow:
/// 1. Sets up test data with tracking entries that have work to do
/// 2. Launches the backfill worker with a terminate signal
/// 3. Allows the worker time to process the data
/// 4. Sets the terminate signal to stop the worker
/// 5. Verifies that all expected processing occurred
async fn test_backfill_worker_helper(has_completed_initial_backfill: bool) -> Result<()> {
    let tmp_dir_config = TmpDirConfigWithStore::new();
    let wal = tmp_dir_config.config.wal.clone();
    let global_store = tmp_dir_config.config.global_store.clone();
    let completed_initial_backfill_ts = if has_completed_initial_backfill {
        Some(Utc::now())
    } else {
        None
    };

    // Create test data with un-backfilled tracking entries
    let tracking_entry = BackfillTrackingEntry {
        project_id: "test_project".to_string(),
        object_type: ObjectType::Experiment,
        last_processed_sequence_id: 0,
        last_encountered_sequence_id: 100, // Has work to do
        last_processed_sequence_id_2: 0,
        last_encountered_sequence_id_2: 50, // Has work to do
        completed_initial_backfill_ts: completed_initial_backfill_ts.clone(),
    };

    let brainstore_objects = vec![
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("worker_obj1".to_string()).unwrap(),
            },
            is_logs2: false,
            sequence_id: 50,
            xact_id: TransactionId(301),
        },
        TestingOnlyBackfillBrainstoreObjectAtom {
            project_id: "test_project".to_string(),
            object_id: FullObjectIdOwned {
                object_type: ObjectType::Experiment,
                object_id: ObjectIdOwned::new("worker_obj2".to_string()).unwrap(),
            },
            is_logs2: true,
            sequence_id: 25,
            xact_id: TransactionId(302),
        },
    ];

    // Insert test data
    insert_object_atoms_into_wal(
        brainstore_objects,
        vec![tracking_entry.clone()],
        wal.clone(),
        global_store.clone(),
    )
    .await?;

    // Verify initial state - tracking entry should have work to do
    let initial_entries = global_store
        .query_unbackfilled_tracking_entries_ordered(has_completed_initial_backfill, None, 10)
        .await?;
    assert_eq!(initial_entries.len(), 1);
    let initial_entry = &initial_entries[0];
    assert_eq!(initial_entry.last_processed_sequence_id, 0);
    assert_eq!(initial_entry.last_processed_sequence_id_2, 0);

    // Set up terminate signal for controlling the worker and channel for
    // learning about worker progress
    let terminate_signal = Arc::new(AtomicBool::new(false));
    let (testing_signal_produced_entry_sender, testing_signal_produced_entry_receiver) =
        async_channel::unbounded::<()>();

    // Clone the config to avoid lifetime issues
    let config = tmp_dir_config.config.clone();
    let process_wal_opts = ProcessObjectWalOptions::default();
    let terminate_signal_clone = terminate_signal.clone();

    let compaction_loop = CompactionLoop::default();

    // Start the worker in a separate task
    let compaction_loop_clone = compaction_loop.clone();
    let worker_handle = tokio::spawn(async move {
        let worker_input = BackfillWorkerInput {
            config,
            process_wal_opts: process_wal_opts.clone(),
            compaction_loop: compaction_loop_clone,
        };

        let worker_optional_input = BackfillWorkerOptionalInput {
            terminate_signal: Some(terminate_signal_clone),
            testing_signal_produced_entry: Some(testing_signal_produced_entry_sender),
        };

        let worker_options = BackfillWorkerOptions {
            num_backfill_workers_realtime: if has_completed_initial_backfill { 1 } else { 0 },
            num_backfill_workers_historical: if has_completed_initial_backfill { 0 } else { 1 },
            backfill_one_tracking_entry_opts: BackfillOneTrackingEntryOptions::default(),
        };

        backfill_worker(worker_input, worker_optional_input, worker_options).await;
    });

    // Wait until we've received the testing signal
    testing_signal_produced_entry_receiver.recv().await.unwrap();

    // Set terminate signal to stop the worker
    terminate_signal.store(true, std::sync::atomic::Ordering::Relaxed);

    // Wait for the worker to finish
    worker_handle.await?;

    // Verify that backfilling occurred
    let final_entry = global_store
        .query_backfill_tracking_entries_by_ids(&[tracking_entry.id()])
        .await?
        .remove(0)
        .unwrap();
    assert_eq!(
        final_entry.last_processed_sequence_id, 100,
        "Regular logs should be processed up to sequence ID 100"
    );
    assert_eq!(
        final_entry.last_processed_sequence_id_2, 50,
        "Logs2 should be processed up to sequence ID 50"
    );

    // Verify that objects were processed by checking WAL entries
    let expected_objects = vec![
        (ObjectId::new("worker_obj1").unwrap(), 1),
        (ObjectId::new("worker_obj2").unwrap(), 1),
    ];

    for (object_id, expected_count) in expected_objects {
        let full_object_id = FullObjectId {
            object_type: ObjectType::Experiment,
            object_id,
        };
        let wal_entries = stream_object_wal(full_object_id, &tmp_dir_config.config).await?;
        assert_eq!(
            wal_entries.len(),
            expected_count,
            "Object {:?} should have exactly {} WAL entries after worker processing",
            object_id,
            expected_count
        );
    }

    // Verify that some segment ids were compacted
    compaction_loop
        .pop_front()
        .expect("Compaction queue should not be empty");

    Ok(())
}

#[tokio::test]
async fn test_backfill_worker_completed_initial_backfill() -> Result<()> {
    test_backfill_worker_helper(true).await
}

#[tokio::test]
async fn test_backfill_worker_no_completed_initial_backfill() -> Result<()> {
    test_backfill_worker_helper(false).await
}
