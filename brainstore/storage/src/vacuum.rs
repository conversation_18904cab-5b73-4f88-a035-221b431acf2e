use clap::<PERSON><PERSON><PERSON>;
use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON><PERSON>, Serialize, Deserialize)]
pub struct CommonVacuumOptions {
    // To avoid interfering with in-progress read/write operations, don't delete files
    // last modified within the last `vacuum_deletion_grace_period_seconds`.
    #[arg(
        long,
        default_value_t = default_vacuum_deletion_grace_period_seconds(),
        help = "Grace period for deleting files.",
        env = "BRAINSTORE_VACUUM_DELETION_GRACE_PERIOD_SECONDS",
    )]
    #[serde(default = "default_vacuum_deletion_grace_period_seconds")]
    pub vacuum_deletion_grace_period_seconds: i64,
    // This accounts for the fact that SegmentVacuumState::last_written_ts isn't a precise "commit
    // timestamp", since writes can continue briefly after a segment op commits its metadata update.
    #[arg(
        long,
        default_value_t = default_vacuum_last_written_slop_seconds(),
        help = "Buffer period to account for writes that continue after the segment's last_written timestamp is committed.",
        env = "BRAINSTORE_VACUUM_LAST_WRITTEN_SLOP_SECONDS",
    )]
    #[serde(default = "default_vacuum_last_written_slop_seconds")]
    pub vacuum_last_written_slop_seconds: i64,
    #[arg(
        long,
        default_value_t = default_deletion_log_batch_size(),
        help = "Batch size for deletion log files.",
        env = "BRAINSTORE_VACUUM_DELETION_LOG_BATCH_SIZE",
    )]
    #[serde(default = "default_deletion_log_batch_size")]
    pub vacuum_deletion_log_batch_size: usize,
}

impl Default for CommonVacuumOptions {
    fn default() -> Self {
        Self {
            vacuum_deletion_grace_period_seconds: default_vacuum_deletion_grace_period_seconds(),
            vacuum_last_written_slop_seconds: default_vacuum_last_written_slop_seconds(),
            vacuum_deletion_log_batch_size: default_deletion_log_batch_size(),
        }
    }
}

// This plus `default_vacuum_last_written_slop_seconds` sum to 1 day to make the resulting
// query filter (see `query_vacuum_segment_ids`) easier to reason about, but the exact
// values don't really matter so long as we don't interfere with ongoing write operations.
fn default_vacuum_deletion_grace_period_seconds() -> i64 {
    23 * 60 * 60 // 23 hours
}

fn default_vacuum_last_written_slop_seconds() -> i64 {
    60 * 60 // 1 hour
}

fn default_deletion_log_batch_size() -> usize {
    1000
}

#[derive(Copy, Clone, Debug, Eq, PartialEq, Serialize, Deserialize)]
pub enum VacuumType {
    #[serde(rename = "vacuum_index")]
    VacuumIndex,
    #[serde(rename = "vacuum_segment_wal")]
    VacuumSegmentWal,
}

impl VacuumType {
    pub fn lock_name(self) -> String {
        self.to_string()
    }
}

impl std::fmt::Display for VacuumType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            VacuumType::VacuumIndex => "vacuum_index",
            VacuumType::VacuumSegmentWal => "vacuum_segment_wal",
        };
        write!(f, "{}", s)
    }
}
