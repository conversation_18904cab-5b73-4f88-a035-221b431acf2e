use std::sync::Arc;

use crate::base::{AppState, BaseArgs, CLIArgs, VacuumObjectIdArgs, VacuumOptions};
use clap::{Parser, Subcommand};
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::vacuum_index::{
    vacuum_index, vacuum_index_stateless, VacuumIndexFullOptions, VacuumIndexInput,
    VacuumIndexOptionalInput,
};
use storage::vacuum_segment_wal::{
    vacuum_segment_wal, vacuum_segment_wal_stateless, VacuumSegmentWalFullOptions,
    VacuumSegmentWalInput, VacuumSegmentWalOptionalInput,
};
use tracing::instrument;
use util::{
    anyhow::{anyhow, Result},
    system_types::{FullObjectId, FullObjectIdOwned},
    uuid::Uuid,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum VacuumCommands {
    Index(CLIArgs<VacuumIndexStatelessFullArgs>),
    SegmentWal(CLIArgs<VacuumSegmentWalStatelessArgs>),
}

pub fn base_args(args: &VacuumCommands) -> &BaseArgs {
    match args {
        VacuumCommands::Index(a) => &a.base,
        VacuumCommands::SegmentWal(a) => &a.base,
    }
}

pub async fn main(args: VacuumCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        VacuumCommands::Index(a) => run_vacuum_index_stateless(app_state, a.args).await,
        VacuumCommands::SegmentWal(a) => run_vacuum_segment_wal_stateless(app_state, a.args).await,
    }
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumStatelessObjectIdArgs {
    #[arg(help = "Object IDs to vacuum")]
    #[serde(default)]
    pub object_ids: Vec<String>,

    #[arg(long, help = "Vacuum all objects")]
    #[serde(default)]
    pub all: bool,
}

fn parse_vacuum_stateless_object_id_args(
    args: &VacuumStatelessObjectIdArgs,
) -> Result<Option<Vec<FullObjectIdOwned>>> {
    if args.all && !args.object_ids.is_empty() {
        return Err(anyhow!("Cannot specify object IDs and also specify --all"));
    }
    let object_ids: Option<Vec<FullObjectIdOwned>> = if args.all {
        None
    } else {
        Some(
            args.object_ids
                .iter()
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?,
        )
    };
    Ok(object_ids)
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct VacuumIndexStatelessArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: VacuumStatelessObjectIdArgs,

    #[arg(
        long,
        help = "Segment ID cursor, as returned by a previous partial run. Pass this to resume from the last successfully processed segment ID"
    )]
    #[serde(default)]
    pub segment_id_cursor: Option<Uuid>,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files"
    )]
    #[serde(default)]
    pub dry_run: bool,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct VacuumIndexStatelessFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: VacuumIndexStatelessArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: VacuumIndexFullOptions,
}

#[instrument(err, skip(app_state))]
pub async fn run_vacuum_index_stateless(
    app_state: Arc<AppState>,
    full_args: VacuumIndexStatelessFullArgs,
) -> Result<util::Value> {
    let object_ids = parse_vacuum_stateless_object_id_args(&full_args.args.object_id_args)?;
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let output = vacuum_index_stateless(
        VacuumIndexInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: app_state.config.index.clone(),
            locks_manager: app_state.config.locks_manager.as_ref(),
            config_file_schema: app_state.config_schema.clone(),
            dry_run: full_args.args.dry_run,
        },
        VacuumIndexOptionalInput {
            segment_id_cursor: full_args.args.segment_id_cursor,
            ..Default::default()
        },
        full_args.options,
    )
    .await;

    Ok(json!(output))
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumIndexFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: VacuumIndexArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: VacuumIndexFullOptions,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct VacuumIndexArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: VacuumObjectIdArgs,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files",
        env = "BRAINSTORE_VACUUM_INDEX_DRY_RUN"
    )]
    #[serde(default)]
    pub dry_run: bool,
}

pub async fn vacuum_index_worker_task(
    app_state: Arc<AppState>,
    object_ids: Option<Vec<FullObjectIdOwned>>,
    args: VacuumOptions,
) {
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    if let Some(object_ids) = &object_ids {
        log::info!(
            "Launching vacuum-index worker with {} object IDs: {:?}",
            object_ids.len(),
            object_ids
        );
    } else {
        log::info!("Launching vacuum-index worker for all objects");
    }

    let full_opts = VacuumIndexFullOptions {
        common_vacuum_opts: args.common_vacuum_opts.clone(),
        vacuum_index_opts: args.vacuum_index_opts.clone(),
    };

    let mut iterations = 0;
    loop {
        log::debug!("Running vacuum-index loop (iter {})", iterations);

        match vacuum_index(
            VacuumIndexInput {
                object_ids: object_ids_slice,
                global_store: app_state.config.global_store.clone(),
                index_store: app_state.config.index.clone(),
                locks_manager: app_state.config.locks_manager.as_ref(),
                config_file_schema: app_state.config_schema.clone(),
                dry_run: args.vacuum_dry_run,
            },
            VacuumIndexOptionalInput::default(),
            &full_opts,
        )
        .await
        {
            Ok(_) => {}
            Err(e) => {
                tracing::error!("Error running vacuum-index: {:?}", e);
            }
        };

        if let Some(max_iterations) = args.background_vacuum_opts.background_vacuum_max_iterations {
            iterations += 1;
            if iterations >= max_iterations {
                log::info!(
                    "Reached maximum number of iterations ({}), exiting vacuum-index loop",
                    max_iterations
                );
                break;
            }
        }

        log::debug!(
            "Sleeping for {} seconds before next vacuum-index iteration",
            args.background_vacuum_opts.background_vacuum_sleep_seconds
        );

        tokio::time::sleep(std::time::Duration::from_secs(
            args.background_vacuum_opts.background_vacuum_sleep_seconds,
        ))
        .await;
    }
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct VacuumSegmentWalStatelessArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: VacuumStatelessObjectIdArgs,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files"
    )]
    #[serde(default)]
    pub dry_run: bool,

    // In integration tests, it is convenient to wait a short amount of time (exactly equal
    // to the sum of all built-in grace periods) before running the vacuum operation itself.
    // This way, this command is guaranteed to clean up all of the dead entries and files
    // we expect it to.
    #[arg(
        long,
        default_value_t = false,
        help = "(For testing) Sleep for enough time to guarantee that vacuum deletes stale files created before the request was received."
    )]
    #[serde(default)]
    pub wait_for_vacuum_grace_period: bool,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: VacuumSegmentWalFullOptions,
}

#[instrument(err, skip(app_state))]
pub async fn run_vacuum_segment_wal_stateless(
    app_state: Arc<AppState>,
    args: VacuumSegmentWalStatelessArgs,
) -> Result<util::Value> {
    let object_ids = parse_vacuum_stateless_object_id_args(&args.object_id_args)?;
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let output = vacuum_segment_wal_stateless(
        VacuumSegmentWalInput {
            object_ids: object_ids_slice,
            global_store: app_state.config.global_store.clone(),
            index_store: app_state.config.index.clone(),
            locks_manager: app_state.config.locks_manager.as_ref(),
            dry_run: args.dry_run,
        },
        VacuumSegmentWalOptionalInput::default(),
        args.options,
    )
    .await;

    Ok(json!(output))
}

pub async fn vacuum_segment_wal_worker_task(
    app_state: Arc<AppState>,
    object_ids: Option<Vec<FullObjectIdOwned>>,
    args: VacuumOptions,
) {
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    if let Some(object_ids) = &object_ids {
        log::info!(
            "Launching vacuum-segment-wal worker with {} object IDs: {:?}",
            object_ids.len(),
            object_ids
        );
    } else {
        log::info!("Launching vacuum-segment-wal worker for all objects");
    }

    let full_opts = VacuumSegmentWalFullOptions {
        common_vacuum_opts: args.common_vacuum_opts,
        vacuum_segment_wal_opts: args.vacuum_segment_wal_opts,
    };

    let mut iterations = 0;
    loop {
        log::debug!("Running vacuum-segment-wal loop (iter {})", iterations);

        match vacuum_segment_wal(
            VacuumSegmentWalInput {
                object_ids: object_ids_slice,
                global_store: app_state.config.global_store.clone(),
                index_store: app_state.config.index.clone(),
                locks_manager: app_state.config.locks_manager.as_ref(),
                dry_run: args.vacuum_dry_run,
            },
            VacuumSegmentWalOptionalInput::default(),
            &full_opts,
        )
        .await
        {
            Ok(_) => {}
            Err(e) => {
                log::error!("Error running vacuum-segment-wal: {}", e);
            }
        };

        if let Some(max_iterations) = args.background_vacuum_opts.background_vacuum_max_iterations {
            iterations += 1;
            if iterations >= max_iterations {
                log::info!(
                    "Reached maximum number of iterations ({}), exiting vacuum-segment-wal loop",
                    max_iterations
                );
                break;
            }
        }

        log::debug!(
            "Sleeping for {} seconds before next vacuum-segment-wal iteration",
            args.background_vacuum_opts.background_vacuum_sleep_seconds
        );

        tokio::time::sleep(std::time::Duration::from_secs(
            args.background_vacuum_opts.background_vacuum_sleep_seconds,
        ))
        .await;
    }
}
