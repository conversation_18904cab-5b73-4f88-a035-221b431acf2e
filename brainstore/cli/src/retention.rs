use lazy_static::lazy_static;
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::base::{self, AppState, CLIArgs, TimeBasedRetentionWorkerOptions};
use crate::{http_client::get_http_client, pg_pool::get_pg_conn};
use clap::{Parser, Subcommand};
use serde::{Deserialize, Serialize};
use serde_json::json;
use storage::service_token::query_service_token;
use storage::{
    global_store::TimeBasedRetentionStats,
    retention_policy_lookup::ControlPlaneContext,
    retention_worker::{
        time_based_retention, time_based_retention_stateless, TimeBasedRetentionInput,
        TimeBasedRetentionOptionalInput, TimeBasedRetentionOptions,
    },
};
use tracing::instrument;
use util::{
    anyhow::{anyhow, Result},
    system_types::{FullObjectId, FullObjectIdOwned},
    url::Url,
};

#[derive(Subcommand, Debug, Clone, Serialize, Deserialize)]
pub enum RetentionCommands {
    TimeBasedRetention(CLIArgs<TimeBasedRetentionStatelessFullArgs>),
}

pub fn base_args(args: &RetentionCommands) -> &base::BaseArgs {
    match args {
        RetentionCommands::TimeBasedRetention(a) => &a.base,
    }
}

pub async fn main(args: RetentionCommands) -> Result<util::Value> {
    let app_state = AppState::new(base_args(&args))?;
    match args {
        RetentionCommands::TimeBasedRetention(a) => {
            run_time_based_retention_stateless(app_state, a.args).await
        }
    }
}

#[derive(Debug, Clone)]
pub struct TimeBasedRetentionArgs<'a> {
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub control_plane_ctx: &'a ControlPlaneContext,
    pub dry_run: bool,
}

#[derive(Debug, Clone)]
pub struct TimeBasedRetentionFullArgs<'a> {
    pub args: TimeBasedRetentionArgs<'a>,
    pub options: TimeBasedRetentionOptions,
}

#[derive(Debug, Clone, Default, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionStatelessObjectIdArgs {
    #[arg(help = "Object IDs to run time-based retention on")]
    #[serde(default)]
    pub object_ids: Vec<String>,

    #[arg(long, help = "Run time-based retention on all objects")]
    #[serde(default)]
    pub all: bool,
}

#[derive(Parser, Debug, Clone, Serialize, Deserialize)]
pub struct TimeBasedRetentionStatelessArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub object_id_args: TimeBasedRetentionStatelessObjectIdArgs,

    #[arg(
        long,
        default_value_t = false,
        help = "Dry run, will return the number of planned deletes but not delete any files"
    )]
    #[serde(default)]
    pub dry_run: bool,
}

#[derive(Debug, Clone, Parser, Serialize, Deserialize)]
pub struct TimeBasedRetentionStatelessFullArgs {
    #[command(flatten)]
    #[serde(flatten)]
    pub args: TimeBasedRetentionStatelessArgs,

    #[command(flatten)]
    #[serde(flatten)]
    pub options: TimeBasedRetentionOptions,
}

#[derive(Serialize, Debug, Default, Deserialize)]
pub struct TimeBasedRetentionStatelessOutput {
    pub success: bool,

    #[serde(flatten)]
    #[serde(default)]
    pub stats: TimeBasedRetentionStats,

    pub error: Option<String>,
}

pub async fn get_control_plane_ctx(app_state: Arc<AppState>) -> Result<ControlPlaneContext> {
    let app_url = app_state.base_args.app_url.parse::<Url>()?;
    let service_token = match get_service_token(app_state.clone()).await? {
        Some(api_key) => api_key,
        None => {
            return Err(anyhow!("No service token found"));
        }
    };
    Ok(ControlPlaneContext {
        http_client: get_http_client()?,
        app_url,
        service_token,
    })
}

#[instrument(err, skip(app_state))]
pub async fn run_time_based_retention_stateless(
    app_state: Arc<AppState>,
    full_args: TimeBasedRetentionStatelessFullArgs,
) -> Result<util::Value> {
    if full_args.args.object_id_args.all && !full_args.args.object_id_args.object_ids.is_empty() {
        util::anyhow::bail!("Cannot specify object IDs and also specify --all");
    }

    let object_ids: Option<Vec<FullObjectIdOwned>> = if full_args.args.object_id_args.all {
        None
    } else {
        Some(
            full_args
                .args
                .object_id_args
                .object_ids
                .into_iter()
                .map(|s| s.parse::<FullObjectIdOwned>())
                .collect::<Result<_>>()?,
        )
    };
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let control_plane_ctx = get_control_plane_ctx(app_state.clone()).await?;

    let result = time_based_retention_stateless(
        TimeBasedRetentionInput {
            object_ids: object_ids_slice,
            config: &app_state.config,
            config_file_schema: app_state.config_schema.as_ref(),
            control_plane_ctx: Some(&control_plane_ctx),
            dry_run: full_args.args.dry_run,
        },
        TimeBasedRetentionOptionalInput::default(),
        &full_args.options,
    )
    .await;

    let output = match result {
        Err(e) => TimeBasedRetentionStatelessOutput {
            error: Some(e.to_string()),
            ..Default::default()
        },
        Ok(stats) => TimeBasedRetentionStatelessOutput {
            success: true,
            stats,
            ..Default::default()
        },
    };

    Ok(json!(output))
}

pub async fn time_based_retention_worker_task(
    app_state: Arc<AppState>,
    control_plane_ctx: ControlPlaneContext,
    object_ids: Option<Vec<FullObjectIdOwned>>,
    options: TimeBasedRetentionWorkerOptions,
) {
    let object_ids_refs: Option<Vec<FullObjectId>> = object_ids
        .as_ref()
        .map(|ids| ids.iter().map(|id| id.as_ref()).collect());
    let object_ids_slice: Option<&[FullObjectId]> = object_ids_refs.as_deref();

    let mut iterations = 0;
    loop {
        log::debug!("Running time-based retention loop (iter {})", iterations);

        match time_based_retention(
            TimeBasedRetentionInput {
                object_ids: object_ids_slice,
                config: &app_state.config,
                config_file_schema: app_state.config_schema.as_ref(),
                control_plane_ctx: Some(&control_plane_ctx),
                dry_run: options.time_based_retention_dry_run,
            },
            TimeBasedRetentionOptionalInput::default(),
            &options.time_based_retention_options,
        )
        .await
        {
            Ok(_) => {}
            Err(e) => {
                log::error!("Error running time-based retention: {:?}", e);
            }
        };

        iterations += 1;

        if let Some(max_iterations) = options
            .background_time_based_retention_options
            .background_time_based_retention_max_iterations
        {
            if iterations >= max_iterations {
                log::info!(
                    "Reached maximum number of iterations ({}), exiting time-based retention loop",
                    max_iterations
                );
                break;
            }
        }

        log::debug!(
            "Sleeping for {} seconds before next time-based retention iteration",
            options
                .background_time_based_retention_options
                .background_time_based_retention_sleep_seconds
        );

        tokio::time::sleep(std::time::Duration::from_secs(
            options
                .background_time_based_retention_options
                .background_time_based_retention_sleep_seconds,
        ))
        .await;
    }
}

lazy_static! {
    static ref RETENTION_SERVICE_TOKEN: Mutex<Option<String>> = Mutex::new(None);
}

const RETENTION_SERVICE_TOKEN_NAME: &str = "bt_data_plane_service_token";
const USE_LAZY_SERVICE_TOKEN: bool = false;

pub async fn get_service_token(app_state: Arc<AppState>) -> Result<Option<String>> {
    if USE_LAZY_SERVICE_TOKEN {
        if let Some(token) = RETENTION_SERVICE_TOKEN.lock().await.as_ref() {
            return Ok(Some(token.clone()));
        }
    }

    let pg_conn = get_pg_conn(app_state.clone())?;
    let token = query_service_token(&pg_conn, RETENTION_SERVICE_TOKEN_NAME).await?;

    if USE_LAZY_SERVICE_TOKEN {
        if let Some(token) = &token {
            *RETENTION_SERVICE_TOKEN.lock().await = Some(token.clone());
        }
    }

    Ok(token)
}
