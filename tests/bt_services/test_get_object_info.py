import braintrust
from braintrust_local.constants import ANON_USER_ID

from tests.braintrust_app_test_base import LOCAL_API_URL, BraintrustAppTestBase


class GetObjectInfoTest(BraintrustAppTestBase):
    def test_get_object_info_permisions(self):
        def request_org_object_info(headers):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                json=dict(object_type="organization", object_ids=[self.org_id]),
                headers=headers,
            ).json()

        self_headers = dict(Authorization=f"Bearer {self.org_api_key}")
        resp = request_org_object_info(self_headers)
        self.assertEqual(len(resp), 1)
        self.assertEqual(
            {k: v for k, v in resp[0].items() if k != "permissions"},
            dict(
                object_id=self.org_id,
                object_name=self.org_name,
                parent_cols={},
                is_deleted=False,
            ),
        )
        self.assertGreater(len(resp[0]["permissions"]), 0)

        # Non-org owner in the same org should be able to get object info with
        # no permissions.
        user_id, _, user_api_key = self.createUserInOrg(self.org_id, remove_from_org_owners=True)
        user_headers = dict(Authorization=f"Bearer {user_api_key}")
        resp = request_org_object_info(user_headers)
        self.assertEqual(
            resp,
            [dict(object_id=self.org_id, object_name=self.org_name, parent_cols={}, permissions=[], is_deleted=False)],
        )

        # Member of a different org should not be able to get object info.
        other_org_id, _ = self.createOrg()
        other_user_id, _, other_user_api_key = self.createUserInOrg(other_org_id)
        other_user_headers = dict(Authorization=f"Bearer {other_user_api_key}")
        resp = request_org_object_info(other_user_headers)
        self.assertEqual(resp, [])

        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json=dict(name="test0")).json()
        experiment = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json=dict(project_id=project["id"], name="test0")
        ).json()

        def request_experiment_object_info(headers):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                json=dict(object_type="experiment", object_ids=[experiment["id"]]),
                headers=headers,
            ).json()

        # Before we make the experiment public, the non-owner should have empty,
        # and the non-org member should not be able to get any info.
        base_experiment_object_info = dict(
            object_id=experiment["id"],
            object_name=experiment["name"],
            parent_cols=dict(
                project=dict(id=project["id"], name=project["name"]),
                organization=dict(id=self.org_id, name=self.org_name),
            ),
            permissions=[],
            is_deleted=False,
        )

        resp = request_experiment_object_info(user_headers)
        self.assertEqual(resp, [base_experiment_object_info])
        resp = request_experiment_object_info(other_user_headers)
        self.assertEqual(resp, [])

        # After public, both of them should have access with a read permission.
        self.grant_acl(
            dict(object_type="experiment", object_id=experiment["id"], user_id=ANON_USER_ID, permission="read"),
        )
        modified_experiment_object_info = {**base_experiment_object_info, "permissions": ["read"], "is_deleted": False}
        resp = request_experiment_object_info(user_headers)
        self.assertEqual(resp, [modified_experiment_object_info])
        resp = request_experiment_object_info(other_user_headers)
        self.assertEqual(resp, [modified_experiment_object_info])

    def test_get_object_info_empty_project_name(self):
        experiment = braintrust.init("")
        self.assertEqual(experiment.project.name, "")
        resp = self.run_request(
            "post",
            f"{LOCAL_API_URL}/api/self/get_object_info",
            json=dict(object_type="experiment", object_ids=[experiment.id], accept_arbitrary_acl_object_types=True),
        ).json()[0]
        self.assertEqual(resp["object_id"], experiment.id)
        self.assertEqual(resp["object_name"], experiment.name)
        self.assertEqual(
            resp["parent_cols"],
            {
                "project": dict(id=experiment.project_id, name=""),
                "org_project": dict(id=self.org_id, name=self.org_name),
                "organization": dict(id=self.org_id, name=self.org_name),
            },
        )

    def test_is_deleted_field(self):
        # Create a project and an experiment and dataset within that project.
        project = self.run_request("post", f"{LOCAL_API_URL}/v1/project", json={"name": "test-project"}).json()
        experiment = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/experiment", json={"project_id": project["id"], "name": "test-experiment"}
        ).json()
        dataset = self.run_request(
            "post", f"{LOCAL_API_URL}/v1/dataset", json={"project_id": project["id"], "name": "test-dataset"}
        ).json()

        def get_object_info(object_type, object_ids):
            return self.run_request(
                "post",
                f"{LOCAL_API_URL}/api/self/get_object_info",
                json={
                    "object_type": object_type,
                    "object_ids": object_ids,
                    "include_deleted_objects": True,
                },
            ).json()

        # Initially all objects should have is_deleted = False.
        project_info = get_object_info("project", [project["id"]])
        self.assertEqual(len(project_info), 1)
        self.assertEqual(project_info[0]["object_id"], project["id"])
        self.assertFalse(project_info[0]["is_deleted"], "Project should not be deleted initially")

        experiment_info = get_object_info("experiment", [experiment["id"]])
        self.assertEqual(len(experiment_info), 1)
        self.assertEqual(experiment_info[0]["object_id"], experiment["id"])
        self.assertFalse(experiment_info[0]["is_deleted"], "Experiment should not be deleted initially")

        dataset_info = get_object_info("dataset", [dataset["id"]])
        self.assertEqual(len(dataset_info), 1)
        self.assertEqual(dataset_info[0]["object_id"], dataset["id"])
        self.assertFalse(dataset_info[0]["is_deleted"], "Dataset should not be deleted initially")

        # Delete the experiment individually and check is_deleted = True.
        self.run_request("delete", f"{LOCAL_API_URL}/v1/experiment/{experiment['id']}")

        experiment_info_after_delete = get_object_info("experiment", [experiment["id"]])
        self.assertEqual(len(experiment_info_after_delete), 1)
        self.assertTrue(experiment_info_after_delete[0]["is_deleted"], "Experiment should be marked as deleted")

        # Verify project and dataset are still not deleted.
        project_info_after_exp_delete = get_object_info("project", [project["id"]])
        self.assertFalse(project_info_after_exp_delete[0]["is_deleted"], "Project should still not be deleted")
        dataset_info_after_exp_delete = get_object_info("dataset", [dataset["id"]])
        self.assertFalse(dataset_info_after_exp_delete[0]["is_deleted"], "Dataset should still not be deleted")

        # Delete the parent project and verify cascading deletion.
        self.run_request("delete", f"{LOCAL_API_URL}/v1/project/{project['id']}")

        # All objects should now be marked as deleted.
        project_info_deleted = get_object_info("project", [project["id"]])
        self.assertEqual(len(project_info_deleted), 1)
        self.assertEqual(project_info_deleted[0]["object_id"], project["id"])
        self.assertTrue(project_info_deleted[0]["is_deleted"], "Project should be marked as deleted")

        experiment_info_deleted = get_object_info("experiment", [experiment["id"]])
        self.assertEqual(len(experiment_info_deleted), 1)
        self.assertEqual(experiment_info_deleted[0]["object_id"], experiment["id"])
        self.assertTrue(experiment_info_deleted[0]["is_deleted"], "Experiment should still be marked as deleted")

        dataset_info_deleted = get_object_info("dataset", [dataset["id"]])
        self.assertEqual(len(dataset_info_deleted), 1)
        self.assertEqual(dataset_info_deleted[0]["object_id"], dataset["id"])
        self.assertTrue(
            dataset_info_deleted[0]["is_deleted"], "Dataset should now be marked as deleted due to project deletion"
        )
