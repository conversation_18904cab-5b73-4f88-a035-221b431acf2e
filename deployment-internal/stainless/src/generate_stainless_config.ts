import * as fs from "node:fs/promises";
import * as yaml from "yaml";
import pluralize from "pluralize";
import { ArgumentParser, ArgumentDefaultsHelpFormatter } from "argparse";
import { mergeDicts, camelToSnakeCase } from "@braintrust/core";
import {
  EventObjectType,
  objectTypes,
  objectTypesWithEvent,
  getEventObjectType,
} from "@braintrust/typespecs";
import { z } from "zod";

const baseConfig = {
  organization: {
    name: "braintrust",
    docs: "https://www.braintrustdata.com/docs/api/spec",
    contact: "<EMAIL>",
    github_org: "braintrustdata",
  },
  settings: { license: "Apache-2.0" },
  client_settings: {
    opts: {
      api_key: {
        type: "string",
        nullable: true,
        auth: { security_scheme: "bearerAuth" },
        read_env: "BRAINTRUST_API_KEY",
      },
    },
  },
  environments: { production: "https://api.braintrust.dev" },
  query_settings: { nested_format: "brackets", array_format: "comma" },
  readme: {
    example_requests: {
      default: {
        type: "request",
        endpoint: "post /v1/project",
        params: { name: "foobar" },
      },
      headline: {
        type: "request",
        endpoint: "post /v1/project",
        params: { name: "foobar" },
      },
      pagination: { type: "request", endpoint: "get /v1/project", params: {} },
    },
  },
  pagination: [
    {
      name: "list_objects",
      description: "Pagination for endpoints which list data objects",
      type: "cursor_id",
      request: {
        starting_after: {
          type: "string",
          "x-stainless-pagination-property": {
            purpose: "next_cursor_id_param",
          },
        },
        ending_before: {
          type: "string",
          "x-stainless-pagination-property": {
            purpose: "previous_cursor_id_param",
          },
        },
        limit: { type: "integer" },
      },
      response: {
        objects: {
          type: "array",
          items: {
            type: "object",
            properties: { id: { type: "string", format: "uuid" } },
            required: ["id"],
          },
        },
      },
    },
  ],
  resources: {
    $top_level: { methods: { hello_world: "get /v1" } },
  },
  unspecified_endpoints: [
    "post /v1/insert",
    "post /v1/proxy/chat/completions",
    "post /v1/proxy/completions",
    "post /v1/proxy/credentials",
    "post /v1/proxy/auto",
    "post /v1/proxy/embeddings",
    "post /v1/proxy/{path+}",
  ],
} as const;

const subresourceOverride: { [K in EventObjectType]?: string } = {
  project_logs: "logs",
};

const resourceDefOverride: { [K: string]: Record<string, unknown> } = {
  organization: {
    subresources: {
      members: {
        methods: {
          update: `patch /v1/organization/members`,
        },
      },
    },
  },
  env_var: {
    methods: {
      list: {
        paginated: false,
      },
    },
  },
};

const targetDefOverride: Record<string, Record<string, unknown>> = {
  java: {
    reverse_domain: "com.braintrustdata.api",
    publish: {
      maven: {
        artifact_id: "braintrust-java",
        sonatype_platform: "portal",
      },
    },
    // See
    // https://braintrustdata.slack.com/archives/C06EYS88SQM/p1741209057984299
    // for details.
    options: {
      back_compat_java_package_paths: true,
    },
  },
  kotlin: {
    reverse_domain: "com.braintrustdata.api",
    publish: {
      maven: {
        artifact_id: "braintrust-kotlin",
        sonatype_platform: "portal",
      },
    },
    // See
    // https://braintrustdata.slack.com/archives/C06EYS88SQM/p1741209057984299
    // for details.
    options: {
      back_compat_java_package_paths: true,
    },
  },
  go: {
    package_name: "braintrust",
  },
  ruby: {
    gem_name: "braintrust",
    publish: {
      rubygems: false,
    },
  },
  node: {
    package_name: "@braintrust/api",
    production_repo: "braintrustdata/braintrust-api-js",
    publish: {
      npm: true,
    },
  },
  python: {
    package_name: "braintrust_api",
    production_repo: "braintrustdata/braintrust-api-py",
    project_name: "braintrust-api",
    publish: {
      pypi: true,
    },
  },
};

function filterValidMethods({
  objectType,
  openAPISpec,
  objectMethods,
}: {
  objectType: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  openAPISpec: any;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  objectMethods: Record<string, any>;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
}): Record<string, any> {
  const specPaths = openAPISpec["paths"];
  return Object.fromEntries(
    Object.entries(objectMethods)
      .filter(([k, v]) => {
        const pathComponents = v.split(/\s+/);
        if (pathComponents.length !== 2) {
          throw new Error(
            `Invalid path component for ${objectType} op ${k}: ${v}`,
          );
        }
        const method = pathComponents[0];
        const path = pathComponents[1];
        return path in specPaths && method in specPaths[path];
      })
      .map(([k, v]) => [k, { endpoint: v }]),
  );
}

type UnderscorePropertyEntry = {
  pathComponents: string[];
};

function addUnderscorePropertyEntry(
  entries: Record<string, [UnderscorePropertyEntry]>,
  key: string,
  entry: UnderscorePropertyEntry,
) {
  if (key in entries) {
    entries[key].push(entry);
  } else {
    entries[key] = [entry];
  }
}

function mergeUnderscorePropertyEntries(
  mergeInto: Record<string, [UnderscorePropertyEntry]>,
  mergeFrom: Record<string, [UnderscorePropertyEntry]>,
) {
  for (const [key, entries] of Object.entries(mergeFrom)) {
    for (const entry of entries) {
      addUnderscorePropertyEntry(mergeInto, key, entry);
    }
  }
}

function findUnderscorePropertiesHelper(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  specObj: any,
  curPathComponents: string[],
): Record<string, [UnderscorePropertyEntry]> {
  const ret: Record<string, [UnderscorePropertyEntry]> = {};

  if (!(specObj instanceof Object)) {
    return ret;
  }

  if ("properties" in specObj) {
    for (const k of Object.keys(specObj["properties"])) {
      if (k.match(/^_/)) {
        const pathComponents = curPathComponents.concat(["properties", k]);
        addUnderscorePropertyEntry(ret, k, { pathComponents });
      }
    }
  }

  for (const [k, v] of Object.entries(specObj)) {
    mergeUnderscorePropertyEntries(
      ret,
      findUnderscorePropertiesHelper(v, curPathComponents.concat([k])),
    );
  }

  return ret;
}

function findUnderscoreProperties(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
  openAPISpec: any,
): Record<string, [UnderscorePropertyEntry]> {
  return findUnderscorePropertiesHelper(openAPISpec["components"]["schemas"], [
    "$",
    "components",
    "schemas",
  ]);
}

function underscorePropertiesToTransformations(
  underscoreProperties: Record<string, [UnderscorePropertyEntry]>,
): Record<string, unknown>[] {
  const out: Record<string, unknown>[] = [];
  for (const [k, entries] of Object.entries(underscoreProperties)) {
    out.push({
      reason: "Pydantic does not support names starting with '_'",
      command: "renameValue",
      args: {
        filter: {
          only: entries.map((x) => x.pathComponents.join(".")),
        },
        rename: {
          python: {
            property_name: k.replace(/^_*/, ""),
          },
        },
      },
    });
  }
  return out;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function collectEndpointRequestBodySchemas(openApiSpec: any): Set<string> {
  const SCHEMA_PREFIX = "#/components/schemas/";
  const schemas = new Set<string>();
  for (const [_path, pathMethods] of Object.entries(openApiSpec["paths"])) {
    if (!(pathMethods instanceof Object)) {
      continue;
    }
    for (const [_method, methodSpec] of Object.entries(pathMethods)) {
      if ("requestBody" in methodSpec) {
        const requestBody = methodSpec["requestBody"];
        if ("content" in requestBody) {
          for (const [_contentType, contentSpec] of Object.entries(
            requestBody["content"],
          )) {
            if (
              contentSpec instanceof Object &&
              "schema" in contentSpec &&
              contentSpec["schema"] instanceof Object &&
              "$ref" in contentSpec["schema"]
            ) {
              const schemaRef = contentSpec["schema"]["$ref"];
              if (
                typeof schemaRef === "string" &&
                schemaRef.startsWith(SCHEMA_PREFIX)
              ) {
                schemas.add(schemaRef.slice(SCHEMA_PREFIX.length));
              }
            }
          }
        }
      }
    }
  }
  return schemas;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function collectModels(openApiSpec: any): Record<string, string> {
  const models: Record<string, string> = {};
  const skipModelSchemas = collectEndpointRequestBodySchemas(openApiSpec);
  for (const [schemaName, schemaDef] of Object.entries(
    openApiSpec["components"]["schemas"],
  )) {
    if (
      !(schemaDef instanceof Object) ||
      skipModelSchemas.has(schemaName) ||
      !(
        ("type" in schemaDef && schemaDef["type"] === "object") ||
        ("type" in schemaDef && "enum" in schemaDef)
      )
    ) {
      continue;
    }
    models[camelToSnakeCase(schemaName)] = schemaName;
  }
  return models;
}

function resourceName(objectType: string) {
  // Note: despite the fact that the REST API path names are singular (e.g.
  // `v1/experiment`), we pluralize the resource names because that is the
  // convention amongst other SDKs (such as OpenAI chat_completions).
  // If we do change this, there are a few other codegen-related issues with
  // singular names:
  //
  //    - The stainless codegen for NodeJS seems to run into naming conflicts
  //    between singular resource names and the model schemas of the same name,
  //    e.g. `project` and `Project`.
  //
  //    - The api_key resource needs to be named something different because
  //    most stainless objects have an `api_key` member for the braintrust API
  //    key.
  return pluralize(objectType);
}

async function generateConfig(openApiSpecFile: string) {
  const openAPISpec = JSON.parse(
    await fs.readFile(openApiSpecFile, { encoding: "utf-8" }),
  );
  const config = JSON.parse(JSON.stringify(baseConfig));

  // Add resource definitions.
  for (const objectType of z
    .string()
    .array()
    .parse(objectTypes.options)
    .concat(["eval"])) {
    const objectTypeWithEvent = objectTypesWithEvent.safeParse(objectType);
    const eventObjectType = objectTypeWithEvent.success
      ? getEventObjectType(objectTypeWithEvent.data)
      : undefined;
    const subresource = eventObjectType
      ? (subresourceOverride[eventObjectType] ?? eventObjectType)
      : undefined;

    const objectMethods = filterValidMethods({
      objectType,
      openAPISpec,
      objectMethods: {
        create: `post /v1/${objectType}`,
        replace: `put /v1/${objectType}`,
        list: `get /v1/${objectType}`,
        retrieve: `get /v1/${objectType}/{${objectType}_id}`,
        update: `patch /v1/${objectType}/{${objectType}_id}`,
        batch_update: `post /v1/${objectType}/batch_update`,
        delete: `delete /v1/${objectType}/{${objectType}_id}`,
        find_and_delete: `delete /v1/${objectType}`,
        summarize: `get /v1/${objectType}/{${objectType}_id}/summarize`,
        invoke: `post /v1/${objectType}/{${objectType}_id}/invoke`,
      },
    });

    const eventMethods =
      eventObjectType &&
      filterValidMethods({
        objectType,
        openAPISpec,
        objectMethods: {
          insert: `post /v1/${eventObjectType}/{${objectType}_id}/insert`,
          fetch: `get /v1/${eventObjectType}/{${objectType}_id}/fetch`,
          fetch_post: `post /v1/${eventObjectType}/{${objectType}_id}/fetch`,
          feedback: `post /v1/${eventObjectType}/{${objectType}_id}/feedback`,
        },
      });

    if (
      Object.keys(objectMethods).length +
        (eventMethods ? Object.keys(eventMethods).length : 0) ===
      0
    ) {
      continue;
    }

    const resourceDef = {
      methods: objectMethods,
    };
    if (eventMethods) {
      if (objectType == subresource) {
        mergeDicts(resourceDef.methods, eventMethods);
      } else if (subresource) {
        mergeDicts(resourceDef, {
          subresources: { [subresource]: { methods: eventMethods } },
        });
      }
    }
    if (resourceDefOverride[objectType]) {
      mergeDicts(resourceDef, resourceDefOverride[objectType]);
    }

    mergeDicts(config, {
      resources: { [resourceName(objectType)]: resourceDef },
    });
  }

  // Add package definitions.
  for (const target of ["node", "python", "go", "java", "kotlin", "ruby"]) {
    const def = {
      production_repo: `braintrustdata/braintrust-${target}`,
    };
    mergeDicts(def, targetDefOverride[target] ?? {});
    mergeDicts(config, { targets: { [target]: def } });
  }

  // Add transformations.
  const allTransformations: Record<string, unknown>[] = [];
  allTransformations.push(
    ...underscorePropertiesToTransformations(
      findUnderscoreProperties(openAPISpec),
    ),
  );
  // These are due to a stainless bug in the go code generator. We should be
  // able to remove them when the bug is fixed.
  for (const target of [
    "$.components.schemas.ModelParams",
    "$.components.schemas.ChatCompletionContentPart",
  ]) {
    allTransformations.push({
      reason:
        "Generating a flat object here causes issues with property name conflicts",
      command: "mergeObject",
      args: {
        target,
        object: {
          "x-stainless-go-union": {
            flattened: false,
          },
        },
      },
    });
  }
  mergeDicts(config, { openapi: { transformations: allTransformations } });

  // Add all component schemas as models at the "$shared" level.
  mergeDicts(config, {
    resources: { $shared: { models: collectModels(openAPISpec) } },
  });

  return config;
}

async function main() {
  const parser = new ArgumentParser({
    description:
      "Generate the stainless configuration for the REST API. Prints the config to stdout",
    formatter_class: ArgumentDefaultsHelpFormatter,
  });
  parser.add_argument("--openapi-spec", {
    help: "Path to JSON spec file to mirror",
    required: true,
  });
  parser.add_argument("--format", {
    help: "Output format of spec",
    choices: ["yaml", "json"],
    default: "yaml",
  });
  const args = parser.parse_args();

  const config = await generateConfig(args.openapi_spec);
  if (args.format === "yaml") {
    const yamlConfig = `
# NOTE: This file is auto-generated. Instead of directly modifying it, please update
# deployment-internal/stainless/src/generate_stainless_config.ts and re-run
# ./scripts/generate_stainless_config.py.
${yaml.stringify(config)}`.trim();
    console.log(yamlConfig);
  } else {
    console.log(JSON.stringify(config, null, 2));
  }
}

main();
