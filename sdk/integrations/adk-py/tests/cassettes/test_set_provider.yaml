interactions:
- request:
    body: '{"contents": [{"parts": [{"text": "What''s the weather like in New York?"}],
      "role": "user"}], "systemInstruction": {"parts": [{"text": "You are a helpful
      assistant that can check weather and time.\n\nYou are an agent. Your internal
      name is \"weather_time_assistant\"."}], "role": "user"}, "tools": [{"functionDeclarations":
      [{"description": "Get weather for a city.", "name": "get_weather", "parameters":
      {"properties": {"city": {"type": "STRING"}}, "required": ["city"], "type": "OBJECT"}},
      {"name": "get_current_time"}]}], "generationConfig": {}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '548'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-client:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-key:
      - REDACTED
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61RTU/CQBC9769o9kxJoZSqN4MeTECINKgxxgx0KA3b3bq7iEr472y/oEW92UOz
        O+/NvLfzdsSy6AJ4GIegUdEr68VULGuX/zNMcI1cG6AqmWIKUp+4xbernQ1lueELHQs+AMYazSXO
        IUFTpxHqty2CXqGkrXMSyEj90pzZivVX1n6PW+tZyDU9o+zJX7fT+fWkR6VguZ1EhMiqYfuKQJcx
        j9XqAUEJntGmwXhytEvhIxqKKJVinrm1vbbruJ3OpdfrOr7fuXB9tB2PVOK5LN0oiHCEGsze4fhG
        aoYkqQ7EGvlAbPK9e34hVIupgfdLWAsNrIm4rR9T1Y3RjFk9vVqw5vnAytUGt09BLRIzv2Gq2hGp
        rfLc4j+J9ZtapEymCGuGUsVFKhEmJie723bsJQO1svEzzYdSiSoVXOFdmPFm+LiGcdcd+dPhu1aT
        bzlPxHWPkj05AK9x4y8QAwAA
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 19 Aug 2025 20:57:58 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=680
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
- request:
    body: '{"contents": [{"parts": [{"text": "What''s the weather like in New York?"}],
      "role": "user"}, {"parts": [{"functionCall": {"args": {"city": "New York"},
      "name": "get_weather"}}], "role": "model"}, {"parts": [{"functionResponse":
      {"name": "get_weather", "response": {"temperature": 72, "condition": "sunny",
      "city": "New York"}}}], "role": "user"}], "systemInstruction": {"parts": [{"text":
      "You are a helpful assistant that can check weather and time.\n\nYou are an
      agent. Your internal name is \"weather_time_assistant\"."}], "role": "user"},
      "tools": [{"functionDeclarations": [{"description": "Get weather for a city.",
      "name": "get_weather", "parameters": {"properties": {"city": {"type": "STRING"}},
      "required": ["city"], "type": "OBJECT"}}, {"name": "get_current_time"}]}], "generationConfig":
      {}}'
    headers:
      accept:
      - '*/*'
      accept-encoding:
      - gzip, deflate
      connection:
      - keep-alive
      content-length:
      - '802'
      content-type:
      - application/json
      host:
      - generativelanguage.googleapis.com
      user-agent:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-client:
      - google-genai-sdk/1.28.0 gl-python/3.9.21 google-adk/1.9.0 gl-python/3.9.21
      x-goog-api-key:
      - REDACTED
    method: POST
    uri: https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAC/61RwVLCMBC99yt2cqZMoUhbr+pBBUTtoI46TrRLm6FNOkkqMAz/btpSaPVqDpnM
        vpd9u+/tLADyRXnEIqpRkXN4NRWAXXWXmOAauTZAUzLFnEp94tZn13obisZN+YmECcIaqU5QAuMw
        wzW8CLkCpkAVnG9hzXQCFDRmOUqqC4kgluANIcJYIqr+Gyet1vvj+713GkiKFEu1TESYNvR9QyBL
        xplKHpAqwUvaY3g3J0eUfscTEedSfJY72U7fGXsj1w3cYDh2zoJR4PtWI12JkkLRGKeoqTGNHq0h
        pkWW61CskF+IojLNc2uZlscdfOAdcC00TTtQ4PT+tFWXRpSlbe9bsZjtacr0tvL96jkkLYd0d6rG
        Iqvl5O8Z/0ls4HXFrEMydVgLlIrVqcSYmZzsYd+xlylViY2bvOpKJKpccIXXUclbFE8reos3s9mj
        /1FM52KyLcS9T6y99QN15nMSzwIAAA==
    headers:
      Alt-Svc:
      - h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json; charset=UTF-8
      Date:
      - Tue, 19 Aug 2025 20:57:59 GMT
      Server:
      - scaffolding on HTTPServer2
      Server-Timing:
      - gfet4t7; dur=358
      Transfer-Encoding:
      - chunked
      Vary:
      - Origin
      - X-Origin
      - Referer
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - '0'
    status:
      code: 200
      message: OK
version: 1
