name: py

on:
  pull_request:
  push:
    branches: [main]

jobs:
  build:
    runs-on: ${{ matrix.os }}

    strategy:
      # As of 2024-11-08, this test takes roughly 60 seconds. There is not much benefit to fail-fast.
      fail-fast: false
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
        os: [ubuntu-latest, windows-latest]

    env:
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY}}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY}}

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          cd py && make install-dev
      - name: Test whether the Python SDK can be installed
        run: |
          # This is already done by make install-dev, but we're keeping this as a separate step
          # to explicitly verify that installation works
          python -m uv pip install -e ./core/py[all]
          python -m uv pip install -e ./py[all]
          python -m uv pip install -e ./integrations/langchain-py[all]
      - name: Test whether the Python SDK can be imported
        run: |
          python -c 'import braintrust'
      - name: Lint Other stuff
        # lint everything other than the SDK (that's linted in make nox)
        if: ${{ matrix.os == 'ubuntu-latest' }}
        run: |
          pylint --errors-only $(git ls-files '**/*.py' | grep -v '^py\/' | grep -v 'integrations/adk-py/')
      - name: Run tests
        run: |
          python -m unittest discover ./core/py/src
          pytest ./integrations/langchain-py/src

      - name: Run nox tests
        run: |
          make nox

  adk-py:
    uses: ./.github/workflows/adk-py-test.yaml
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]
        os: [ubuntu-latest, windows-latest]
    with:
      python-version: ${{ matrix.python-version }}
      os: ${{ matrix.os }}

  upload-wheel:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"
      - name: Install build dependencies and build wheel
        run: |
          cd py && make install-build-deps && make build
      - name: Upload wheel as artifact
        uses: actions/upload-artifact@v4
        with:
          name: python-wheel
          path: py/dist/*.whl
          retention-days: 5
