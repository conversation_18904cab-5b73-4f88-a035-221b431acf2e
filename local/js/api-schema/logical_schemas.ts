import { z } from "zod";
import {
  experimentEventSchema,
  datasetEventSchema,
  promptSessionEventSchema,
  projectLogsEventSchema,
  promptSchema,
  functionSchema,
} from "@braintrust/typespecs";
import {
  JSONSchemaObject,
  jsonSchemaObjectSchema,
} from "@braintrust/btql/schema";
import { customZodToJsonSchema } from "@braintrust/btql/schema";
import { TRANSACTION_ID_FIELD } from "@braintrust/core";

// Logical schemas
export const braintrustLogicalSchema: z.ZodObject<
  // We blow out typescript's type inference here with errors like
  // "The inferred type of this node exceeds the maximum length the compiler will serialize."
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Record<string, z.ZodArray<z.ZodObject<any, any>>>
> = z.object({
  experiment: z.array(experimentEventSchema),
  dataset: z.array(datasetEventSchema),
  prompt_session: z.array(promptSessionEventSchema),
  playground_logs: z.array(experimentEventSchema),
  project_logs: z.array(projectLogsEventSchema),
  project: z.array(projectLogsEventSchema.merge(experimentEventSchema)),
  project_prompts: z.array(promptSchema),
  org_prompts: z.array(promptSchema),
  project_functions: z.array(functionSchema),
  org_functions: z.array(functionSchema),
});

export const ObjectIdFields = [
  "project_id",
  "experiment_id",
  "dataset_id",
  "prompt_session_id",
  "log_id",
  "org_id",
] as const;

export const commentSchema = z.object({
  text: z.string(),
  parent_id: z.string().nullish(),
  reactions: z.record(z.array(z.string())).nullish(),
});
export type CommentData = z.infer<typeof commentSchema>;

export const auditMergeSchema = z.object({
  action: z.literal("merge"),
  path: z.array(z.string()),
  from: z.any(),
  to: z.any(),
});

export const auditDataSchema = z.union([
  auditMergeSchema,
  z.object({ action: z.literal("delete") }),
  z.object({ action: z.literal("upsert") }),
]);

export type AuditMerge = z.infer<typeof auditMergeSchema>;
export type AuditData = z.infer<typeof auditDataSchema>;

export const auditLogOriginSchema = z.object({
  id: z.string(),
  [TRANSACTION_ID_FIELD]: z.string().nullish(),
});
export type AuditLogOrigin = z.infer<typeof auditLogOriginSchema>;

export const auditLogMetadataSchema = z.record(z.any());

const auditLogBaseSchema = z.object({
  origin: auditLogOriginSchema,
  source: z.string(),
  metadata: auditLogMetadataSchema.nullish(),
  comment: commentSchema.nullish(),
  audit_data: auditDataSchema.nullish(),
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
function buildAuditLogSchema(schema: z.ZodArray<z.ZodObject<any, any>>) {
  const mainFields = Object.fromEntries(
    Object.entries(schema.element.shape).filter(([k, _]) =>
      ["id", "_xact_id", "created"].concat(ObjectIdFields).includes(k),
    ),
  );
  return z.array(
    z.object({
      ...mainFields,
      ...auditLogBaseSchema.shape,
      source: z.string(),
    }),
  );
}

const _nullishStringValue = z.string().nullish();
type ZodNullishStringType = typeof _nullishStringValue;
type ObjectIdFieldsT = (typeof ObjectIdFields)[number];

export const auditLogBaseEventSchema = z.object({
  id: projectLogsEventSchema.shape.id,
  _xact_id: projectLogsEventSchema.shape._xact_id,
  created: projectLogsEventSchema.shape.created,
  ...auditLogBaseSchema.shape,
  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
  ...(Object.fromEntries(
    ObjectIdFields.map((x) => [x, z.string().nullish()]),
  ) as { [X in ObjectIdFieldsT]: ZodNullishStringType }),
});

export const auditLogRowSchema = auditLogBaseEventSchema.and(
  z.record(z.enum(ObjectIdFields), z.string().nullish()),
);

export type AuditLogRow = z.infer<typeof auditLogRowSchema>;

export type BraintrustLogicalSchema = z.infer<typeof braintrustLogicalSchema>;

export const BRAINTRUST_LOGICAL_SCHEMA: JSONSchemaObject =
  jsonSchemaObjectSchema.parse(customZodToJsonSchema(braintrustLogicalSchema));

const braintrustAuditLogSchema = z.object(
  Object.fromEntries(
    Object.entries(braintrustLogicalSchema.shape).map(([k, v]) => [
      k,
      buildAuditLogSchema(v),
    ]),
  ),
);

export type BraintrustAuditLogSchema = z.infer<typeof braintrustAuditLogSchema>;
export const BRAINTRUST_AUDIT_LOG_LOGICAL_SCHEMA: JSONSchemaObject =
  jsonSchemaObjectSchema.parse(customZodToJsonSchema(braintrustAuditLogSchema));
