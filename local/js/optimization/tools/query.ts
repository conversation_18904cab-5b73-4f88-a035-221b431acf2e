import { z } from "zod";
import { type Tool } from "./types";

// Example questions that someone might want to ask:
// What are some common question types in my logs?
// What is causing the most errors?
// Find everything for user X
// Which user is the most active?
// Which model gets used the most?
// Find me examples of users struggling

export const btqlQueryToolParams = z.object({
  explanation: z.string().describe(
    `A brief explanation of what you're trying to find or analyze with this query. This helps provide context for the query results.

Examples:
- "Finding all errors in the last 24 hours to identify what's causing failures"
- "Analyzing which users are most active to understand usage patterns"
- "Looking for examples where users expressed confusion or frustration"
- "Comparing model performance across different scoring dimensions"`,
  ),

  query: z.string().describe(
    `A BTQL query WITHOUT the 'from' clause. The tool will automatically add the correct from clause based on the project context.

IMPORTANT:
- Use dot notation for nested fields! If InferSchemaTool shows ['metadata', 'user_id'], use metadata.user_id in queries.
- Limit statements should not exceed 100 even if asked for more.

Examples of valid queries:
- "select: *" - Get all fields from recent logs
- "select: * | filter: metadata.user_id = 'user123' | limit: 10" - Find logs for specific user (NOT user_id alone!)
- "select: input, output, metadata.model | filter: metadata.model = 'gpt-4'" - Filter by nested field
- "dimensions: metadata.model | measures: count(1) as calls, avg(scores.Factuality) as avg_score" - Group by model
- "dimensions: metadata.user_id | measures: count(1) as activity | sort: activity desc | limit: 10" - Most active users
- "select: * | filter: scores.Factuality > 0.8" - Filter by score value (use scores.Factuality, not just Factuality)
- "dimensions: hour(created) as hour | measures: count(error) as errors | filter: created > now() - interval 1 day" - Hourly errors
- "select: metadata.model, metrics" - Get model and ALL metrics fields (NOT metrics.*, just metrics)`,
  ),

  shape: z
    .enum(["spans", "traces", "summary"])
    .optional()
    .describe(
      `The shape of data to return:
- spans: Returns individual spans (default)
- traces: Returns all spans from traces that contain at least one matching span
- summary: Returns one row per trace with aggregated metrics`,
    ),
  visualizationConfig: z
    .object({
      unitType: z
        .enum(["count", "duration", "percent", "cost"])
        .describe(
          "The type of unit to use. This is what the value on the y-axis or the measure represents.",
        ),
    })
    .optional()
    .describe(
      "The configuration for the visualization. Only use this if you are querying timeseries data. Leave empty if not querying timeseries data.",
    ),
});

export type BTQLQueryToolParameters = z.infer<typeof btqlQueryToolParams>;

export const btqlQueryToolResultSchema = z.object({
  rowCount: z.number().describe("Number of rows returned"),
  schema: z.unknown().describe("JSON schema of the result shape"),
  data: z.array(z.record(z.unknown())).describe("The query results"),
  cursor: z
    .string()
    .optional()
    .describe("Cursor for pagination if more results exist"),
  timeRangeFilter: z
    .union([
      z.string(),
      z.object({
        from: z.string(),
        to: z.string(),
      }),
    ])
    .optional()
    .describe("The time range filter applied to the query"),
  projectId: z.string().describe("The project ID that the query was run on"),
});

export type BTQLQueryToolResult = z.infer<typeof btqlQueryToolResultSchema>;

// Schema inference tool
export const inferSchemaToolParams = z.object({});

export type InferSchemaToolParameters = z.infer<typeof inferSchemaToolParams>;

export const inferSchemaToolResultSchema = z.object({
  data: z
    .array(
      z.object({
        name: z
          .array(z.union([z.string(), z.number()]))
          .describe("Field path as array (e.g., ['metadata', 'user_id'])"),
        type: z
          .object({
            type: z.string().optional(),
          })
          .optional()
          .describe("The field's data type"),
        top_values: z
          .array(
            z.object({
              value: z.unknown().describe("The actual value"),
              count: z.number().describe("How many times this value appears"),
            }),
          )
          .describe("Most common values for this field, sorted by frequency"),
      }),
    )
    .describe(
      "All fields found in the data with their types and most common values",
    ),
});

export type InferSchemaToolResult = z.infer<typeof inferSchemaToolResultSchema>;

export const InferSchemaTool: Tool<
  InferSchemaToolParameters,
  InferSchemaToolResult
> = {
  description: `Analyze the structure and content of logs by inferring the schema and showing the most common values for each field.

This tool discovers:
- All available fields in your logs (input, output, metadata.*, scores.*, etc.)
- The data type of each field (string, number, array, object)
- The most frequently occurring values for each field with their counts
- The complete field paths for use in BTQL queries

For each field, you'll see:
- The full path as an array (e.g., ['metadata', 'user_id'] which translates to metadata.user_id in BTQL)
- The data type (string, number, boolean, etc.)
- Top 10 most common values sorted by frequency with exact counts

This is essential for:
- Discovering what fields exist before writing queries
- Understanding the actual values in your data (e.g., what user IDs exist, what models are used)
- Finding the exact field paths for BTQL queries
- Identifying patterns in your data (e.g., most common inputs, frequent error types)
- Understanding cardinality (how many unique values exist)

Example insights from the tool:
- See all metadata fields like metadata.user_id, metadata.model, metadata.session_id
- Discover custom fields you may not know about
- Find the most active users or most used models
- Understand what values to filter on in your queries`,

  parameters: inferSchemaToolParams,
};

export const BTQLQueryTool: Tool<BTQLQueryToolParameters, BTQLQueryToolResult> =
  {
    description: `Execute BTQL (Braintrust Query Language) queries to analyze logs, experiments, and traces.

BTQL is a SQL-like language for querying AI application data. Key capabilities:

CLAUSES:
- select: Choose fields (use * for all fields)
- filter: Apply conditions (supports =, !=, >, <, >=, <=, IS NULL, LIKE, MATCH, CONTAINS)
- sort: Order results (field asc/desc)
- limit: Limit results (default 100)
- dimensions/measures: For aggregations (count, sum, avg, min, max, percentile)
- unpivot: Expand arrays into individual rows (e.g., unpivot: tags as tag)

FIELDS BY SHAPE:

For 'spans' and 'traces':
- id: Unique span identifier
- span_id: Current span ID
- root_span_id: Root span ID for the trace
- created: Timestamp when created
- input/output: The prompts and responses
- expected: Expected output (if available)
- error: Error information (if any)
- metadata.* : Nested fields MUST use dot notation (e.g., metadata.user_id, metadata.model, metadata.session_id)
- scores.* : Nested score fields (e.g., scores.Factuality, scores.Coherence, scores.Helpfulness)
- metrics.* : Nested metrics (e.g., metrics.tokens, metrics.latency, metrics.cost)
- tags: Array of tags applied to the span
- span_attributes.name/type/purpose: Span classification (use dot notation: span_attributes.name)
- origin: Information about where the span was created

For 'summary' (aggregated per trace):
- id: Root span ID (trace identifier)
- comparison_key: Key for comparing traces
- created: Timestamp of the trace
- input/output/expected/error/metadata: From root span only (truncated to preview_length)
- tags/origin: Full arrays from root span
- scores.*: Averaged across all spans in the trace
- metrics.duration/llm_duration: Timing metrics
- metrics.prompt_tokens/completion_tokens/total_tokens: Summed across trace
- metrics.estimated_cost: Total estimated cost
- span_type_info: Summary of span types in the trace

OPERATORS:
- Text: MATCH (semantic search for finding content), = (standard equality)
- Arrays: CONTAINS (check if array contains value, e.g., tags CONTAINS 'production')
- Logic: AND, OR, NOT
- Time: created > now() - interval 1 day
- IS NULL, IS NOT NULL for checking null values
- =, != for exact matches
- >, <, >=, <= for numeric comparisons

FUNCTIONS:
- Time: day(), hour(), month(), year(), now()
- Aggregates: count(), sum(), avg(), min(), max(), percentile()
- String: lower(), upper(), concat()
- Array: len() (array length), CONTAINS (check if value in array)
- Conditional: Use ternary operator (a ? b : c) for conditional logic, NOT CASE WHEN

FIELD ACCESS RULES:
- ALWAYS use dot notation for nested fields
- If InferSchemaTool shows ['metadata', 'user_id'], write it as: metadata.user_id
- If InferSchemaTool shows ['scores', 'Factuality'], write it as: scores.Factuality
- NEVER use just 'user_id' or 'Factuality' - always include the parent object
- NO WILDCARD SUBFIELDS: To get all fields from an object, project the object itself
  - CORRECT: select: metadata, metrics (gets all metadata fields and all metrics fields)
  - WRONG: select: metadata.*, metrics.* (this syntax does not exist)
- If you use the summary shape, you can only \`select: *\`.

SORT: NEVER add sort clauses when asked for "most recent", "latest", "newest", or "last" items
- Results are automatically sorted by timestamp in descending order (newest first)
- Just use limit to get the desired number of recent items
- Example: "find the most recent error" → "filter: error IS NOT NULL | select: * | limit: 1"
- Example: "show the latest 5 failures" → "filter: error IS NOT NULL | select: * | limit: 5"

LITERAL VALUES:
- Strings: to escape quotes, use backslash (e.g., 'This is a string with a quote: \\'')

WORKING WITH ARRAYS (e.g., tags):
- Filter by exact tag: filter: tags CONTAINS 'production'
- Multiple tags: filter: tags CONTAINS 'production' AND tags CONTAINS 'high_priority'
- Non-empty array: filter: tags IS NOT NULL AND len(tags) > 0
- Expand array for grouping: unpivot: tags as tag | dimensions: tag | measures: count(1)

IMPORTANT ARRAY LIMITATIONS:
- You CANNOT use LIKE with arrays: tags LIKE 'environment:%' is INVALID
- You CANNOT filter on unpivoted columns: after unpivot: tags as tag, you cannot use tag in filters
- To find tags matching a pattern, you must either:
  1. Filter by exact values: filter: tags CONTAINS 'environment:production'
  2. Or unpivot and group all tags, then look for patterns in the results

SPANS AND TRACES:
- When you are querying with the 'spans' shape (the default) or 'traces' shape, you will receive one
  row per span. That means you should be prepared to handle data from subspans, or filter them out.
- To only consider root spans, you can add the \`is_root\` expression to your query. This is useful
  when you are grouping by an attribute in metadata, for example, that is only present on the root span.
- If you see a lot of null values while grouping, you are probably grouping by a field that is not present
  on all spans. Consider using the \`is_root\` expression to only consider root spans, or an "IS NOT NULL"
  filter on your grouping field.

EXAMPLES:
1. Find recent errors: filter: error IS NOT NULL AND created > now() - interval 1 day
2. Group by model: dimensions: metadata.model | measures: count(1) as calls, avg(scores.Factuality) as avg_score
3. Filter by user: filter: metadata.user_id = '3e29ac38-5588-412a-a91c-7498c2f7277e' (NOT just user_id = ...)
4. User activity: dimensions: metadata.user_id | measures: count(1) as activity | sort: activity desc
5. Score filtering: filter: scores.Factuality > 0.8 AND scores.Helpfulness > 0.7
6. Conditional grouping: dimensions: error as has_error | measures: count(1) as count
7. Calculate error rate: measures: count(error) as errors, count(1) as total
8. Most popular tag: unpivot: tags as tag | dimensions: tag | measures: count(1) as usage | sort: usage desc | limit: 1
9. Tag percentage: measures: count(tags CONTAINS 'experimental' ? 1 : null) as exp_count, count(1) as total
10. All tag distribution: unpivot: tags as tag | dimensions: tag | measures: count(1) as count (then filter results for patterns)
11. Specific environment: filter: tags CONTAINS 'environment:production' (must know exact value, not pattern)`,

    parameters: btqlQueryToolParams,
  };
