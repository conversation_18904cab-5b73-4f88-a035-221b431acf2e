import {
  <PERSON>l,
  NOOP_SPAN,
  Span,
  _internalGetGlobalState,
  currentSpan,
  initExperiment,
} from "braintrust";
import { make<PERSON><PERSON><PERSON><PERSON>, SpanChatLogger, MODEL } from "./eval-vars";
import { Score, Factuality } from "autoevals";
import { PROJECT_NAME } from "./meta-eval";
import { ChatMetrics } from "../llm/chat";
import {
  ALL_TOOL_NAMES,
  BTQLQueryToolParameters,
  BTQLQueryToolResult,
} from "../tools";
import { parseQuery } from "@braintrust/btql/parser";
import { bindQuery } from "@braintrust/btql/binder";
import { BRAINTRUST_LOGICAL_SCHEMA } from "@lib/api-schema";
import { btqlQueriesDataset } from "./datasets/btql-queries";

interface BTQLQueryOutput {
  result: string | undefined;
  metrics: ChatMetrics;
}

// Score the result using Factuality
async function scoreResultAccuracy({
  input,
  output,
  expected,
}: {
  input: string;
  output: BTQLQueryOutput;
  expected: string;
}): Promise<Score> {
  if (!output.result) {
    return {
      name: "result_accuracy",
      score: 0,
      metadata: { error: "No result generated" },
    };
  }

  return await Factuality({
    input: input,
    output: output.result,
    expected: expected,
  });
}

// Score tool success rate
function toolSuccessRate({ output }: { output: BTQLQueryOutput }): Score {
  const metrics = output.metrics;
  return {
    name: "tool_success_rate",
    score: metrics.toolCalls > 0 ? metrics.completed / metrics.toolCalls : null,
    metadata: {
      completed: metrics.completed,
      toolCalls: metrics.toolCalls,
      errors: metrics.errors,
    },
  };
}

// Score that there are no invalid tool calls
function noInvalidToolCalls({ output }: { output: BTQLQueryOutput }): Score {
  return {
    name: "no_invalid_tool_calls",
    score: output.metrics.invalidToolCalls === 0 ? 1 : 0,
    metadata: {
      invalidToolCalls: output.metrics.invalidToolCalls,
    },
  };
}

// Score that btql_query was used
function btqlQueryUsed({ output }: { output: BTQLQueryOutput }): Score {
  const btqlUsed = output.metrics.toolCallsByType?.btql_query > 0;
  return {
    name: "btql_query_used",
    score: btqlUsed ? 1 : 0,
    metadata: {
      toolCallsByType: output.metrics.toolCallsByType,
    },
  };
}

function wrapBTQLTool(
  handler: (args: BTQLQueryToolParameters) => Promise<BTQLQueryToolResult>,
  experimentId: string,
) {
  return async (
    args: BTQLQueryToolParameters,
  ): Promise<BTQLQueryToolResult> => {
    // Try parsing the query ourselves.
    const fullQuery = `from: experiment('${experimentId}') ${args.shape ?? ""} | ${args.query}`;

    let parsed;
    try {
      parsed = parseQuery(fullQuery);
      currentSpan().log({
        scores: {
          parses: 1,
          limitExceeded: parsed.limit && parsed.limit > 100 ? 1 : 0,
        },
      });
    } catch (e) {
      currentSpan().log({
        scores: { parses: 0 },
      });
      throw e;
    }

    let bound;
    try {
      bound = bindQuery({ query: parsed, schema: BRAINTRUST_LOGICAL_SCHEMA });
      currentSpan().log({
        scores: { binds: 1 },
      });
    } catch (e) {
      currentSpan().log({
        scores: { binds: 0 },
      });
      throw e;
    }

    if ("select" in bound && bound.select.length > 0) {
      // A select+sort is bad because it can be very slow
      currentSpan().log({
        scores: { noSlowSort: (bound.sort?.length ?? 0) === 0 ? 1 : 0 },
      });
    }

    try {
      const result = await handler(args);
      currentSpan().log({
        scores: { btqlSucceeds: 1 },
      });
      return result;
    } catch (e) {
      currentSpan().log({
        scores: { btqlSucceeds: 0 },
      });
      throw e;
    }
  };
}

Eval(PROJECT_NAME, {
  data: async () => {
    const containerExperiment = initExperiment(PROJECT_NAME + " eval logs");

    // Create synthetic log data that supports all our test queries
    const users = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>",
    ];
    const models = ["gpt-4", "gpt-3.5-turbo", "claude-3", "claude-2"];
    const questionTypes = [
      "How do I debug this error?",
      "What's the best practice for authentication?",
      "Can you explain this concept?",
      "Help me optimize this code",
      "Why is my application slow?",
    ];

    const environments = ["production", "staging", "development"];
    const priorities = ["high", "medium", "low"];

    // Generate varied log entries
    for (let i = 0; i < 50; i++) {
      const userId = users[i % users.length];
      const model = models[Math.floor(i / 3) % models.length];
      const input = questionTypes[i % questionTypes.length];

      // Create tags with varying patterns
      const tags: string[] = [];

      // Environment tag - most common
      const environment = environments[i % environments.length];
      tags.push(`environment:${environment}`);

      // High priority for some requests
      if (i % 5 === 0) {
        tags.push("high_priority");
        tags.push("priority:high");
      } else if (i % 3 === 0) {
        tags.push(`priority:${priorities[i % priorities.length]}`);
      }

      // Experimental tag for some requests
      if (model === "claude-3" || i % 8 === 0) {
        tags.push("experimental");
      }

      // Feature flags
      if (i % 4 === 0) {
        tags.push("feature_flag:new_ui");
      }

      const span = containerExperiment.startSpan({
        name: "chat_completion",
        type: "llm",
        event: {
          input: input,
          output: `Here's a helpful response to: ${input}`,
          metadata: {
            user_id: userId,
            model: model,
            timestamp: new Date().toISOString(),
            session_id: `session_${Math.floor(i / 5)}`,
          },
          metrics: {
            start: Date.now() / 1001 - i * 100,
          },
          tags: tags,
        },
      });

      // Add some entries with errors
      if (i % 7 === 0) {
        span.log({
          error: "Rate limit exceeded",
        });
      }

      // Add some "struggling" examples
      if (userId === "<EMAIL>" && i % 3 === 0) {
        span.log({
          input:
            "I'm really struggling to understand async/await. Nothing makes sense.",
          metadata: {
            user_id: userId,
            model: model,
            sentiment: "frustrated",
          },
          tags: tags,
        });
      }

      // Make alice the most active user
      if (userId === "<EMAIL>") {
        // Log additional activity for alice
        const subSpan = span.startSpan({
          type: "llm",
          event: {
            input: `Follow-up question ${i}: ${input}`,
            output: "Additional response",
            metadata: {
              user_id: userId,
              model: model,
            },
          },
        });
        subSpan.end();
      }

      span.end();
    }

    // Ensure gpt-4 is the most used model by adding extra entries
    for (let i = 0; i < 20; i++) {
      // Create tags for these additional entries
      const tags: string[] = [];

      // These are production gpt-4 requests
      tags.push("environment:production");

      // Some are high priority
      if (i % 3 === 0) {
        tags.push("high_priority");
        tags.push("priority:high");
      }

      // Feature flags for some
      if (i % 5 === 0) {
        tags.push("feature_flag:advanced_reasoning");
      }

      const span = containerExperiment.startSpan({
        type: "llm",
        event: {
          input: questionTypes[i % questionTypes.length],
          output: "GPT-4 response",
          metadata: {
            user_id: users[i % users.length],
            model: "gpt-4",
          },
          tags: tags,
        },
      });
      span.end();
    }

    await containerExperiment.flush();
    const experimentId = await containerExperiment.id;

    return await Promise.all(
      btqlQueriesDataset.map(async (testCase) => {
        const query = `from: experiment('${experimentId}') ${testCase.shape ?? ""} | ${testCase.expectedQuery}`;
        const result = await _internalGetGlobalState().apiConn().post("/btql", {
          query,
        });
        const data = await result.json();
        return {
          input: testCase.input,
          expected: `This is the raw data that answers the query: ${JSON.stringify(data.data, null, 2)}`,
          metadata: {
            ...testCase.metadata,
            query: testCase.expectedQuery,
            experiment_id: experimentId,
          },
        };
      }),
    );
  },
  task: async (input, hooks): Promise<BTQLQueryOutput> => {
    let consoleLogger: SpanChatLogger | undefined = undefined;
    let rawOutput: Span = NOOP_SPAN;

    try {
      const {
        chat,
        consoleLogger: cl,
        rawOutput: ro,
      } = await makeEvalChat(null, {
        allowed_tools: ALL_TOOL_NAMES.filter(
          (tool) => tool === "btql_query" || tool === "infer_schema",
        ),
        queryObject: {
          objectType: "experiment",
          objectId: hooks.metadata.experiment_id,
        },
        wrapTools: (tools) => {
          tools.updateImplementations({
            // @ts-ignore
            btql_query: wrapBTQLTool(tools.tools.btql_query),
          });
        },
      });
      consoleLogger = cl;
      rawOutput = ro;

      const result = await chat.turn(
        `Answer the following question. The final answer should be 1 sentence.\n\n${input}`,
      );
      return {
        result: result.text,
        metrics: chat.metrics,
      };
    } finally {
      await consoleLogger?.flush();
      rawOutput.end();
    }
  },
  scores: [
    scoreResultAccuracy,
    toolSuccessRate,
    noInvalidToolCalls,
    btqlQueryUsed,
  ],
  experimentName: `BTQL Query Generation - ${MODEL}`,
  metadata: {
    model: MODEL,
    description: "Tests BTQL query generation for various scenarios",
  },
});
