import { z } from "zod";
import { LexoRank } from "lexorank";
import { Span, SpanData } from "./span";
import {
  ChatCompletionAssistantMessageParam,
  ChatCompletionCreateParams,
  ChatCompletionTool,
  ChatCompletionToolChoiceOption,
} from "openai/resources";
import {
  attachmentReferenceSchema,
  Message,
  promptDataSchema,
  responsesAPIFormatSchema,
} from "@braintrust/typespecs";
import { modelParamToModelParam } from "@braintrust/proxy/schema";
import { IdField, TransactionIdField } from "../query";
import { isArray, isObject } from "@braintrust/core";
import { ReadonlyAttachment } from "braintrust";

export function parsePromptFromSpan(span: Span) {
  try {
    if (span.data.span_attributes?.type !== "llm") return false;
    return parsePromptSpanData(span.data);
  } catch {
    return false;
  }
}

export function parsePromptSpanData(promptRow: SpanData) {
  const input = JSON.parse(promptRow.input);
  const {
    model,
    functions: functionsArg,
    function_call: functionCallArg,
    tools: toolsArg,
    tool_choice: toolChoiceArg,
    response_format: responseFormatArg,
    text: responseTextFormatArg,
    ...params
  } = JSON.parse(promptRow.metadata || "{}");

  if (functionsArg && toolsArg) {
    throw new Error("Unsupported: both functions and tools set");
  }
  if (functionCallArg && toolChoiceArg) {
    throw new Error("Unsupported: both function_call and tool_choice set");
  }

  const tools: Array<ChatCompletionTool> | undefined = functionsArg
    ? // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      (functionsArg as Array<ChatCompletionCreateParams.Function>).map((f) => ({
        type: "function",
        function: f,
      }))
    : toolsArg;

  const tool_choice: ChatCompletionToolChoiceOption | undefined =
    functionCallArg
      ? {
          function:
            // prettier-ignore
            (
              // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
              functionCallArg as ChatCompletionAssistantMessageParam.FunctionCall
            ).name,
          type: "function",
        }
      : toolChoiceArg;

  const knownParams = Object.fromEntries(
    Object.entries(params).filter(
      ([key, value]) => modelParamToModelParam[key],
    ),
  );

  let response_format = responseFormatArg;

  if (!response_format && responseTextFormatArg) {
    const parsedTextResponseFormat = z
      .object({
        format: responsesAPIFormatSchema.optional(),
      })
      .safeParse(responseTextFormatArg);
    if (parsedTextResponseFormat.success) {
      response_format = {
        type: "json_schema",
        json_schema: parsedTextResponseFormat.data.format,
      };
    }
  }

  const parsed = promptDataSchema.safeParse({
    prompt: {
      type: "chat",
      messages: input,
      tools: tools ? JSON.stringify(tools, null, 2) : undefined,
    },
    options: {
      position: LexoRank.middle().toString(),
      model,
      params: {
        ...knownParams,
        response_format,
        tool_choice,
      },
    },
  });

  return parsed;
}

export const promptSpanSchema = z.object({
  id: z.string(),
  _xact_id: z.string(),
  created: z.unknown(),
  span_id: z.string(),
  root_span_id: z.string(),
  is_root: z.boolean().nullish(),
  span_parents: z.array(z.string()).nullish(),
  span_attributes: z.object({
    name: z.string(),
    type: z.string().optional(),
  }),
  project_id: z.string(),
  log_id: z.string(),
  input: z.unknown(),
  output: z.unknown(),
  metadata: z.record(z.unknown()),
  metrics: z.record(z.number()),
});
export type PromptSpan = z.infer<typeof promptSpanSchema>;

// NOTE(austin): Right now this function is only used in OTEL expect tests
// to assert that LLM calls reconstructed from OTEL spans match the original
// LLM calls. At some point we may want to unify this with similar codepaths
// in the app such as `ai/use-chat.ts`.
export async function promptSpanToPrompt({
  promptSpan,
}: {
  promptSpan: PromptSpan;
}): Promise<unknown> {
  const span: Span = {
    id: promptSpan.id,
    span_id: promptSpan.span_id,
    root_span_id: promptSpan.root_span_id,
    data: {
      log_id: promptSpan.log_id,
      [IdField]: promptSpan.id,
      [TransactionIdField]: promptSpan._xact_id,
      span_id: promptSpan.span_id,
      span_attributes: {
        name: promptSpan.span_attributes.name,
        type: promptSpan.span_attributes.type,
      },
      scores: {},
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      metrics: promptSpan.metrics as SpanData["metrics"],
      input: JSON.stringify(promptSpan.input),
      metadata: JSON.stringify(promptSpan.metadata),
    },
    scores: {},
    parent_span_id: promptSpan.span_parents?.[0] ?? null,
    children: [],
    span_parents: promptSpan.span_parents ?? undefined,
  };

  const promptData = parsePromptFromSpan(span);
  if (!promptData || !promptData.success) {
    return undefined;
  }

  if (promptData.data.prompt?.type === "chat") {
    await resolveAttachmentInMessages(promptData.data.prompt.messages);
  }

  const { prompt, options } = promptData.data;
  if (!prompt) {
    return undefined;
  }

  const { model, params: btParams } = options ?? {};
  const { use_cache: _use_cache, ...params } = btParams ?? {};

  const promptValue =
    prompt.type === "completion" ? prompt.content : prompt.messages;
  const promptArg =
    typeof prompt === "string"
      ? { prompt: promptValue }
      : { messages: promptValue };

  let tools: ChatCompletionTool[] | undefined = undefined;
  if ("tools" in prompt && prompt.tools) {
    try {
      tools = JSON.parse(prompt.tools);
    } catch (e) {
      throw new Error(`Invalid tools JSON: ${e}`);
    }
  }

  return {
    ...promptArg,
    model,
    ...(params ?? {}),
    tools,
  };
}

async function resolveAttachmentInMessages(messages: Message[]) {
  await Promise.all(
    messages.map(async (message) => {
      if (isArray(message.content)) {
        for (const part of message.content) {
          if (isObject(part) && part.type === "image_url") {
            try {
              const attachmentReference = attachmentReferenceSchema.parse(
                JSON.parse(part.image_url.url),
              );
              const attachment = new ReadonlyAttachment(attachmentReference);
              const base64Url = await attachment.asBase64Url();
              part.image_url.url = base64Url;
            } catch {
              // fall through
            }
          }
        }
      }
      return message;
    }),
  );
}
