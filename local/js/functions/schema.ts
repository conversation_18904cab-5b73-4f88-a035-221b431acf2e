import { Score } from "@braintrust/core";
import {
  functionIdSchema,
  invokeFunctionNonIdArgsSchema,
  invokeFunctionSchema,
  functionSchema,
  FunctionObjectType,
  FunctionFormat,
  FunctionOutputType,
  envVarSchema,
} from "@braintrust/typespecs";
import { ROW_REF_FIELD } from "../constants";
import { rowRefSchema } from "../api-schema/insert_schemas";
import { z } from "zod";

export const DEFAULT_FUNCTION_CACHE_TTL = 900;

export const INVOKE_API_VERSION = 1;
export const apiVersionSchema = z.object({
  api_version: z.number().optional().default(INVOKE_API_VERSION),
});

export const scoreSchema = z.union([
  z.object({
    name: z.string(),
    score: z.number().min(0).max(1).nullable().default(null), // Sometimes we get an empty value over the wire
    metadata: z.record(z.unknown()).optional(),
  }),
  z.number().min(0).max(1),
  z.boolean().transform((b) => (b ? 1 : 0)),
  z.null(),
]);

export const invocableLambdaFunctionSchema = z.strictObject({
  type: z.literal("lambda"),
  credentials: z.strictObject({
    AccessKeyId: z.string(),
    SecretAccessKey: z.string(),
    SessionToken: z.string().optional(),
  }),
  expiresAt: z.number(),
  functionArn: z.string(),
  responseStream: z.boolean(),
});
export type InvocableLambdaFunction = z.infer<
  typeof invocableLambdaFunctionSchema
>;

export const invokeMethodSchema = z.union([
  z.strictObject({
    type: z.literal("vm"),
  }),
  invocableLambdaFunctionSchema,
]);
export type InvokeMethod = z.infer<typeof invokeMethodSchema>;

export const encryptedEnvSecretSchema = envVarSchema.extend({
  iv: z.string(),
  data: z.string(),
});
export type EncryptedEnvSecret = z.infer<typeof encryptedEnvSecretSchema>;

export const invocableFunctionSchema = functionSchema
  .omit({
    id: true,
    project_id: true,
    org_id: true,
    log_id: true,
  })
  .merge(
    z.object({
      // We don't need these to be uuids while invoking them, e.g.
      // global functions don't set them.
      id: z.string(),
      project_id: z.string(),
      prompt_session_id: z.string().optional(),
      org_id: z.string(),
      invoke_method: invokeMethodSchema.optional(),
      env_secrets: z.array(encryptedEnvSecretSchema).optional(),
      // This field is not really used, but .strip() also doesn't work properly
      // on omitted fields, so parsing will fail unless we include it :(
      log_id: z.string().optional(),
    }),
  );
export type InvocableFunction = z.infer<typeof invocableFunctionSchema>;

export const authInfoResponse = z
  .strictObject({
    org_info: z.array(
      z
        .strictObject({
          id: z.string(),
          name: z.string(),
          api_url: z.string(),
          realtime_url: z.string(),
          git_metadata: z.unknown(),
        })
        .strip(),
    ),
  })
  .strip();

const invokeRequestInternalFieldsSchema = z
  .object({
    update_score: z
      .object({
        object_type: z.enum(["project_logs", "playground_logs", "experiment"]),
        object_id: z
          .string()
          .describe("The id of the container object you are logging to"),
        row_id: z.string().describe("Id of the row to update"),
        token: z
          .string()
          .describe("Async scoring token that launched the update"),
      })
      .nullish()
      .describe(
        "If specified, log the result of the function invocation as a score to the specified row. In this case, the function output must be a number between 0 and 1, inclusive",
      ),
    /*
    input: z.unknown().nullish().describe("The input to the function"),
    metadata: z
      .record(z.string(), z.unknown())
      .nullish()
      .describe("The metadata to the resulting span"),
      */
    org_name: z.string().nullish(),
    timeout_ms: z.number().max(30000).optional(),
    strict: z
      .boolean()
      .optional()
      .describe(
        "If true, throw an error if one of the variables in the prompt is not present in the input",
      ),
  })
  .and(apiVersionSchema);

export const invokeRequestSchema = invokeFunctionSchema.and(
  invokeRequestInternalFieldsSchema,
);

export type InvokeRequestSchema = z.infer<typeof invokeRequestSchema>;

export const invokeAsyncBatchRequestSchema = z.object({
  function_ids: functionIdSchema.array(),
  non_id_args: invokeFunctionNonIdArgsSchema.and(
    invokeRequestInternalFieldsSchema,
  ),
});

// Sometimes the input to a function is a reference to a row in the database.
export const invokeAsyncInputRefSchema = z.object({
  [ROW_REF_FIELD]: rowRefSchema,
  fields: z.array(z.string()),
});

export type InvokeAsyncInputRef = z.infer<typeof invokeAsyncInputRefSchema>;

export function scoreToScoreObject(
  name: string,
  score: z.infer<typeof scoreSchema>,
): Score {
  if (typeof score === "number" || score === null) {
    return {
      name,
      score,
    };
  } else {
    return score;
  }
}

export type DerivableFunction = Pick<
  InvocableFunction,
  "function_type" | "function_data"
>;

export function deriveFunctionObjectType(
  func: DerivableFunction,
): FunctionObjectType {
  if (!func.function_data || func.function_data.type === "prompt") {
    return "prompt";
  } else if (func.function_type === "scorer") {
    return "scorer";
  } else if (func.function_type === "tool") {
    return "tool";
  } else {
    return "task";
  }
}

export function deriveFunctionFormat(func: DerivableFunction): FunctionFormat {
  if (func.function_data.type === "prompt" || func.function_type === "llm") {
    return "llm";
  } else if (func.function_data.type === "global") {
    return "global";
  } else if (func.function_data.type === "code") {
    return "code";
  } else if (func.function_data.type === "graph") {
    return "graph";
  } else if (func.function_data.type === "remote_eval") {
    throw new Error("Remote evals cannot be used on their own");
  } else {
    const foo: never = func.function_data;
    throw new Error(`Unknown function data type: ${foo}`);
  }
}

export function deriveFunctionOutputType(
  func: DerivableFunction,
): FunctionOutputType {
  if (func.function_data.type === "prompt" || func.function_type === "llm") {
    return "completion";
  } else if (func.function_type === "scorer") {
    return "score";
  } else {
    return "any";
  }
}

const scoringFunctionIdSchema = z.union([
  z.object({
    function_id: z.string(),
  }),
  z.object({
    global_function: z.string(),
  }),
]);
export const onlineScoringTestResultsSchema = z.array(
  z.object({
    row: z.object({
      input: z.unknown(),
      output: z.unknown(),
      expected: z.unknown(),
      metadata: z.unknown(),
    }),
    results: z.array(
      z.union([
        scoringFunctionIdSchema.and(
          z.object({
            kind: z.literal("success"),
            result: scoreSchema,
          }),
        ),
        scoringFunctionIdSchema.and(
          z.object({
            kind: z.literal("error"),
            error: z.string(),
          }),
        ),
      ]),
    ),
  }),
);

export type OnlineScoringTestResults = z.infer<
  typeof onlineScoringTestResultsSchema
>;
