import type { SessionContextValue } from "next-auth/react";
import { type User, organizationSchema } from "@braintrust/typespecs";
import { z } from "zod";
import { type BtSession } from "./auth/server-session";
import { _urljoin } from "@braintrust/core";

export const V1_PROXY_SUFFIX = "/v1/proxy";
export const MultiTenantApiURL = process.env.NEXT_PUBLIC_MULTI_TENANT_API_URL!;
export const MultiTenantRealtimeURL =
  process.env.NEXT_PUBLIC_MULTI_TENANT_REALTIME_URL!;
export const MultiTenantUniversalProxyUrl = makeUniversalProxyUrl(
  // This is a dumb wart -- at build time, MultiTenantApiURL might actually be
  // undefined, so we need to make sure we ?? it away from crashing.
  MultiTenantApiURL ?? "",
);

const maxOverWindowSchema = z.object({
  window_size_days: z.number().int().positive(),
  max_value: z.number().nonnegative(),
});

const maxOverCalendarMonthSchema = z.object({
  window_size_months: z.number().int().positive(),
  max_value: z.number().nonnegative(),
});

export const resourcesSchema = z.object({
  org_id: organizationSchema.shape.id,
  forbid_toggle_experiment_public_to_private: z.boolean().nullish(),
  forbid_insert_datasets: z.boolean().nullish(),
  forbid_insert_prompt_sessions: z.boolean().nullish(),
  forbid_access_sql_explorer: z.boolean().nullish(),
  num_private_experiment_row_actions: maxOverWindowSchema.nullish(),
  num_private_experiment_row_actions_calendar_months:
    maxOverCalendarMonthSchema.nullish(),
  num_production_log_row_actions: maxOverWindowSchema.nullish(),
  num_production_log_row_actions_calendar_months:
    maxOverCalendarMonthSchema.nullish(),
  num_dataset_row_actions: maxOverWindowSchema.nullish(),
  num_dataset_row_actions_calendar_months: maxOverCalendarMonthSchema.nullish(),
  num_log_bytes: maxOverWindowSchema.nullish(),
  num_log_bytes_calendar_months: maxOverCalendarMonthSchema.nullish(),
});

// If you add a field to this schema, make sure to adjust the queries in
// getUserContext and adminFetchOrgContextInfo.
export const orgContextSchema = z
  .strictObject({
    id: organizationSchema.shape.id.optional(),
    name: organizationSchema.shape.name,
    api_url: organizationSchema.shape.api_url.transform(
      (x) => x || MultiTenantApiURL,
    ),
    // If is_universal is null, that means it's unspecified in the organization's
    // configuration. If false, we know it's not universal (yet) and if true we
    // know that it is (and can generate the proxy url accordingly).
    is_universal_api: organizationSchema.shape.is_universal_api.default(null),
    proxy_url: organizationSchema.shape.proxy_url.transform(
      (x) => x || MultiTenantUniversalProxyUrl,
    ),
    realtime_url: organizationSchema.shape.proxy_url.transform(
      (x) => x || MultiTenantRealtimeURL,
    ),
    resources: resourcesSchema.omit({ org_id: true }).nullish(),
    plan_id: z.string().nullish(),
  })
  .transform((org) => {
    // If an org is universal, we can replace the proxy url with its universal
    // proxy url.
    if (
      org.api_url &&
      org.is_universal_api &&
      org.proxy_url === MultiTenantUniversalProxyUrl
    ) {
      org.proxy_url = makeUniversalProxyUrl(org.api_url);
    }
    return org;
  });

export type OrgContextT = z.infer<typeof orgContextSchema>;

export interface UserContextT {
  session: BtSession | null;
  user: User | undefined;
  orgs: Record<string, OrgContextT>;
  status: SessionContextValue["status"];
  isAdmin: boolean;
}

export function makeUniversalProxyUrl(baseUrl: string) {
  return _urljoin(baseUrl, V1_PROXY_SUFFIX);
}

export function normalizeProxyUrlBase(proxyUrl: string) {
  return proxyUrl.endsWith(V1_PROXY_SUFFIX)
    ? proxyUrl.slice(0, proxyUrl.length - V1_PROXY_SUFFIX.length)
    : proxyUrl;
}

export function isBraintrustDataPlane(apiUrl: string) {
  return (
    apiUrl === MultiTenantApiURL ||
    apiUrl === "https://staging-api.braintrust.dev"
  );
}
