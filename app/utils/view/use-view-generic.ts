import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  apiDelete,
  apiPatch,
  apiPostCors,
  apiFetchGetCors,
} from "#/utils/btapi/fetch";
import { BT_FOUND_EXISTING_HEADER, _urljoin } from "@braintrust/core";
import {
  type AclObjectType,
  type ViewType,
  type ViewOptions,
  type ViewData,
  type View as DBView,
  type MonitorViewOptions,
} from "@braintrust/typespecs";
import { type BtSessionToken } from "#/utils/auth/session-token";
import { toastAndLogError } from "#/utils/view/view-utils";

export type DBMonitorView = Omit<DBView, "options"> & {
  options: {
    viewType: "monitor";
    options: MonitorViewOptions;
  };
};

export type View<T extends DBView | DBMonitorView = DBView> = Omit<
  Partial<T>,
  "id"
> & {
  id: string | null;
} & {
  builtin?: boolean;
};

export type ViewParams = {
  objectType: AclObjectType;
  objectId: string;
  viewType: ViewType;
};

export type GetViewsArgs = {
  apiUrl: string;
  getOrRefreshToken: () => Promise<BtSessionToken>;
  viewParams: ViewParams;
};

async function getViews({
  apiUrl,
  getOrRefreshToken,
  viewParams,
}: GetViewsArgs): Promise<View[] | null> {
  const sessionToken = await getOrRefreshToken();
  if (sessionToken === "loading") {
    throw new Error("Session token is still loading");
  }
  if (sessionToken === "unauthenticated") {
    throw new Error("User is not authenticated");
  }
  const { objectType, objectId, viewType } = viewParams;
  const params = new URLSearchParams({
    object_type: objectType,
    object_id: objectId,
    view_type: viewType,
  });
  const url = _urljoin(apiUrl, "/v1/view?") + new URLSearchParams(params);
  const resp = await apiFetchGetCors(url, sessionToken);
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
  return (await resp.json()).objects;
}

const makeRequiredPatchParams = (viewParams: ViewParams) => ({
  object_type: viewParams.objectType,
  object_id: viewParams.objectId,
});

// Individual hooks for each operation
export function useLoadViews({
  queryKey,
  viewParams,
  getViewsArgs,
  enabled = true,
}: {
  queryKey: unknown[];
  viewParams: ViewParams;
  getViewsArgs: GetViewsArgs;
  enabled?: boolean;
}) {
  return useQuery({
    queryKey,
    queryFn: async () => {
      const sessionToken = await getViewsArgs.getOrRefreshToken();
      return sessionToken === "unauthenticated"
        ? []
        : await getViews(getViewsArgs);
    },
    enabled: enabled && !!getViewsArgs.apiUrl && !!viewParams.objectId,
    meta: {
      disableGlobalErrorToast: true,
      globalErrorSentryContext: {
        tags: {
          feature: "views",
          action: "get",
        },
      },
    },
  });
}

export function useCreateView({
  queryKey,
  viewParams,
  getViewsArgs,
  loadView,
}: {
  queryKey: unknown[];
  viewParams: ViewParams;
  getViewsArgs: GetViewsArgs;
  loadView: (view: View | null) => void;
}) {
  const queryClient = useQueryClient();

  const createViewMutationFn = async ({
    name,
    viewData,
    options,
  }: {
    name: string;
    viewData?: ViewData;
    options?: ViewOptions;
  }) => {
    const sessionToken = await getViewsArgs.getOrRefreshToken();
    if (sessionToken === "loading" || sessionToken === "unauthenticated") {
      throw new Error("Invalid session token");
    }
    const payload: Omit<View, "id"> = {
      object_type: viewParams.objectType,
      object_id: viewParams.objectId,
      view_type: viewParams.viewType,
      name,
      view_data: viewData,
      options,
    };
    const resp = await apiPostCors({
      url: `${getViewsArgs.apiUrl}/v1/view`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    if (resp.headers.has(BT_FOUND_EXISTING_HEADER)) {
      throw new Error(`A view with the name ${name} already exists`);
    }
    return await resp.json();
  };

  return useMutation({
    mutationFn: createViewMutationFn,
    onMutate: async () => await queryClient.cancelQueries({ queryKey }),
    onError: (error) => toastAndLogError("create", error),
    onSuccess: async (result: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) => [
        ...(prev ?? []),
        result,
      ]);
      loadView(result);
    },
  });
}

export function useUpdateView({
  queryKey,
  viewParams,
  getViewsArgs,
  loadView,
}: {
  queryKey: unknown[];
  viewParams: ViewParams;
  getViewsArgs: GetViewsArgs;
  loadView: (view: View | null) => void;
}) {
  const queryClient = useQueryClient();

  const updateViewMutationFn = async ({
    viewId,
    name,
    viewData,
    options,
  }: {
    viewId: string;
    name?: string;
    viewData?: ViewData;
    options?: ViewOptions;
  }) => {
    const sessionToken = await getViewsArgs.getOrRefreshToken();
    if (sessionToken === "loading" || sessionToken === "unauthenticated") {
      throw new Error("Invalid session token");
    }
    const payload = {
      ...makeRequiredPatchParams(viewParams),
      ...(name && { name }),
      ...(options && { options }),
      ...(viewData && { view_data: viewData }),
    };
    const resp = await apiPatch({
      url: `${getViewsArgs.apiUrl}/v1/view/${viewId}`,
      sessionToken,
      payload,
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    if (name && resp.headers.has(BT_FOUND_EXISTING_HEADER)) {
      throw new Error(`A view with the name ${name} already exists`);
    }
    return await resp.json();
  };

  return useMutation({
    mutationFn: updateViewMutationFn,
    onMutate: async () => await queryClient.cancelQueries({ queryKey }),
    onError: (error) => toastAndLogError("update", error),
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.map((v) => (v.id === view.id ? view : v)),
      );
      loadView(view);
      return view;
    },
  });
}

export function useDeleteView({
  queryKey,
  viewParams,
  getViewsArgs,
  loadView,
}: {
  queryKey: unknown[];
  viewParams: ViewParams;
  getViewsArgs: GetViewsArgs;
  loadView: (view: View | null) => void;
}) {
  const queryClient = useQueryClient();

  const deleteViewMutationFn = async (viewId: string) => {
    const sessionToken = await getViewsArgs.getOrRefreshToken();
    if (sessionToken === "loading" || sessionToken === "unauthenticated") {
      throw new Error("Invalid session token");
    }
    const resp = await apiDelete({
      url: `${getViewsArgs.apiUrl}/v1/view/${viewId}`,
      sessionToken,
      payload: makeRequiredPatchParams(viewParams),
      alreadySerialized: false,
    });
    if (!resp.ok) {
      throw new Error(await resp.text());
    }
    return await resp.json();
  };

  return useMutation({
    mutationFn: deleteViewMutationFn,
    onMutate: async (viewId: string) => {
      await queryClient.cancelQueries({ queryKey });
      const previousViews = queryClient.getQueryData<View[]>(queryKey);
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.filter((v) => v.id !== viewId),
      );
      return previousViews;
    },
    onError: (error, _, previousViews) => {
      toastAndLogError("delete", error);
      queryClient.setQueryData<View[]>(queryKey, previousViews);
    },
    onSuccess: async (view: View) => {
      queryClient.setQueryData<View[]>(queryKey, (prev) =>
        prev?.filter((v) => v.id !== view.id),
      );
      loadView(null);
    },
  });
}

// Convenience hook that bundles all operations
export function useViewOperations({
  queryKey,
  viewParams,
  getViewsArgs,
  loadView,
}: {
  queryKey: unknown[];
  viewParams: ViewParams;
  getViewsArgs: GetViewsArgs;
  loadView: (view: View | null) => void;
}) {
  const createView = useCreateView({
    queryKey,
    viewParams,
    getViewsArgs,
    loadView,
  });

  const updateView = useUpdateView({
    queryKey,
    viewParams,
    getViewsArgs,
    loadView,
  });

  const deleteView = useDeleteView({
    queryKey,
    viewParams,
    getViewsArgs,
    loadView,
  });

  return {
    // Mutations
    createView: createView.mutate,
    createViewAsync: createView.mutateAsync,
    isCreating: createView.isPending,
    createError: createView.error,

    updateView: updateView.mutate,
    updateViewAsync: updateView.mutateAsync,
    isUpdating: updateView.isPending,
    updateError: updateView.error,

    deleteView: deleteView.mutate,
    isDeleting: deleteView.isPending,
    deleteError: deleteView.error,
  };
}
