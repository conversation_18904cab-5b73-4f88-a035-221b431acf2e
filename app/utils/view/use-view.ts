import {
  type Dispatch,
  type SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
  useTransition,
} from "react";
import { useQuery } from "@tanstack/react-query";
import {
  type ColumnOrderState,
  type ColumnSizingState,
  type VisibilityState,
} from "@tanstack/react-table";
import {
  chartAggregationTypeEnumSchema,
  defaultEntityStorageValues,
  type Entity,
  type EntityStorageSchema,
  useEntityStorage,
  type Value,
} from "#/lib/clientDataStorage";
import { useOrg, useUser } from "#/utils/user";
import {
  parseAsArrayOf,
  parseAsBoolean,
  parseAsInteger,
  parseAsJson,
  parseAsString,
  parseAsStringLiteral,
  type ParserBuilder,
  useQueryState,
} from "nuqs";
import {
  type AnyClauseSpec,
  type CheckResult,
  type ClauseChecker,
  type ClauseSpec,
  type ClauseType,
  type Search,
  addClause,
  checkTag,
  makeBubble,
  normalizeSearch,
  normalizeUrlSearch,
  urlSearchSchema,
  urlValueToClauses,
} from "#/utils/search/search";
import { apiFetchGetCors } from "#/utils/btapi/fetch";
import {
  type AclObjectType,
  type ViewType,
  type View as DBView,
  type ViewOptions,
  type MonitorViewOptions,
  type TableViewOptions,
} from "@braintrust/typespecs";
import { useFeatureFlags } from "#/lib/feature-flags";
import {
  type BtSessionToken,
  useSessionToken,
} from "#/utils/auth/session-token";
import { _urljoin, getObjValueByPath } from "@braintrust/core";
import {
  noopParser,
  parseAsJsonEncoded,
  parseSelectionTypeState,
  useSearchState,
} from "#/ui/query-parameters";

import useEvent from "react-use-event-hook";
import { ProjectContext } from "#/app/app/[org]/p/[project]/projectContext";
import {
  TABLE_ROW_HEIGHTS,
  type TableRowHeight,
} from "#/ui/table-row-height-toggle";
import { doubleQuote } from "@braintrust/btql/planner";
import { tableLayoutTypes } from "#/ui/table/layout-type-control";
import {
  GROUP_BY_NONE,
  GROUP_BY_NONE_VALUE,
  selectionTypeSchema,
  X_AXIS_EXPERIMENT,
  type SelectionType,
} from "#/ui/charts/selectionTypes";
import {
  type AggregationType,
  aggregationTypeEnumSchema,
} from "#/utils/queries/aggregations";
import { type Annotations, annotationsSchema } from "#/ui/charts/annotations";
import { useNavigationType } from "#/utils/navigation-detection";
import isEqual from "lodash.isequal";
import { z } from "zod";
import { useDebouncedCallback } from "#/utils/useDebouncedCallback";
import { BT_ASSIGNMENTS } from "#/utils/assign";

// Time range can be either a string (like "3d", "7d", "30d") or an object with from/to dates
export type TimeRangeFilter = string | { from: string; to: string };

export type View = Omit<Partial<DBView>, "id"> & {
  id: string | null;
} & {
  builtin?: boolean;
};

type ViewOptionsFields<ExperimentsCharting extends boolean = false> = {
  columnVisibility: Record<string, boolean>;
  columnOrder: ColumnOrderState;
  columnSizing: Record<string, number>;
  grouping: ExperimentsCharting extends true ? SelectionType : string;
  rowHeight: TableRowHeight | null;
  tallGroupRows: boolean;
  layout: string | null;
  chartHeight: number;
  timeRangeFilter: TimeRangeFilter;
} & (ExperimentsCharting extends true ? ExperimentsChartingValues : {});

export type ViewProps<ExperimentsCharting extends boolean = false> =
  ViewOptionsFields<ExperimentsCharting> & {
    search: Search;
    searchDebounced: Search;
    isSearchTransitioning: boolean;
    viewOptionsToSave: ViewOptions;
    setSearch: Dispatch<SetStateAction<Search>>;
    setColumnVisibility: Dispatch<SetStateAction<VisibilityState>>;
    setColumnOrder: Dispatch<SetStateAction<ColumnOrderState>>;
    setColumnSizing: Dispatch<SetStateAction<ColumnSizingState>>;
    setGrouping: Dispatch<
      SetStateAction<ExperimentsCharting extends true ? SelectionType : string>
    >;
    setRowHeight: Dispatch<SetStateAction<TableRowHeight | null>>;
    setTallGroupRows: Dispatch<SetStateAction<boolean>>;
    setLayout: (value: "list" | "grid" | "summary" | null) => void;
    setChartHeight: Dispatch<SetStateAction<number>>;
    setTimeRangeFilter: Dispatch<SetStateAction<TimeRangeFilter>>;
    isPending: boolean;
    pageIdentifier: string;
    viewParams: ViewParams | undefined;
    currentView: View | undefined;
    clauseChecker?: ClauseChecker | null;
    resetState: (
      view: View | null,
      options?: { initialize?: boolean; reset?: boolean },
    ) => void;
    isDirty: boolean;
  } & (ExperimentsCharting extends true ? ExperimentsChartingSetters : {});

type ExperimentsChartingSetters = {
  setExcludedMeasures: Dispatch<SetStateAction<SelectionType[]>>;
  setYMetric: Dispatch<SetStateAction<SelectionType | null>>;
  setXAxis: Dispatch<SetStateAction<SelectionType | null>>;
  setSymbolGrouping: Dispatch<SetStateAction<SelectionType | null>>;
  setXAxisAggregation: Dispatch<SetStateAction<AggregationType | "all" | null>>;
  setChartAnnotations: Dispatch<SetStateAction<Annotations>>;
};

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const experimentsChartingSchema = z.object({
  excludedMeasures: z.array(selectionTypeSchema),
  yMetric: selectionTypeSchema.nullish(),
  xAxis: selectionTypeSchema,
  symbolGrouping: selectionTypeSchema,
  xAxisAggregation: chartAggregationTypeEnumSchema,
  chartAnnotations: annotationsSchema,
});

type ExperimentsChartingValues = z.infer<typeof experimentsChartingSchema>;

// probably should hash this at some point so we can store it in the url
const IGNORED_URL_KEYS = new Set([
  "columnVisibility",
  "columnOrder",
  "columnSizing",
  // since localstorage keys are different from the view keys
  "columnOrderConfiguration",
  "columnWidthConfiguration",
]);

const DEFAULT_TIME_RANGE_FILTER = "3d";
const DEFAULT_TABLE_ROW_HEIGHT = "compact";
const DEFAULT_X_AXIS_AGGREGATION = "avg";
const enabledViewFields: Partial<{
  [key in
    | keyof Partial<TableViewOptions>
    | keyof Partial<MonitorViewOptions>]: string[];
}> = {
  timeRangeFilter: ["logs"],
};

function isViewFieldEnabled(
  fieldName:
    | keyof Partial<TableViewOptions>
    | keyof Partial<MonitorViewOptions>,
  viewType: ViewType | undefined,
): boolean {
  const enabledTypes = enabledViewFields[fieldName];
  return enabledTypes ? enabledTypes.includes(viewType ?? "") : false;
}

export function useViewState<
  E extends Entity,
  K extends keyof EntityStorageSchema[E],
>({
  viewParams,
  pageIdentifier,
  entityStorageKey,
  viewField,
  queryParamState,
  defaultValue,
}: {
  viewParams: ViewParams | undefined;
  pageIdentifier: string;
  entityStorageKey: {
    entityType: E;
    identifier: string;
    key: K;
    defaultValue?: Value<E, K>;
  };
  queryParamState: {
    queryKey: string;
    parser: ParserBuilder<NonNullable<Value<E, K>>>;
  };
  viewField: keyof TableViewOptions | keyof MonitorViewOptions;
  // to compare dirty states
  defaultValue: Value<E, K>;
}) {
  const { data, isPending } = useViewQuery({ viewParams, pageIdentifier });
  const [viewName] = useQueryState("v", parseAsString);
  const currentView = useMemo(() => {
    return data?.find((v) => v.name === viewName);
  }, [data, viewName]);

  const [urlState, _setUrlState] = useQueryState(
    queryParamState.queryKey,
    queryParamState.parser,
  );

  const savedViewValue = getViewValue<E, K>(currentView ?? null, viewField);
  const setUrlState = useEvent((value: SetStateAction<typeof urlState>) => {
    // a little bit hacky here:
    // the nuqs setter only calculates if the setter argument itself is the default value,
    // not if the post-parsed value is a default value, so exit early here
    // https://github.com/47ng/nuqs/blob/35933596ec401e795a86f1db351ea9f16bf9b84e/packages/nuqs/src/useQueryState.ts#L274
    if (queryParamState.parser === noopParser) {
      _setUrlState(null);
      return;
    }

    // withDefault doesn't support dynamically updating default values, so we have to handle it ourselves
    const nextValue = value instanceof Function ? value(urlState) : value;
    _setUrlState(
      isEqual(nextValue, savedViewValue ?? defaultValue) ? null : nextValue,
    );
  });

  const [state, setState] = useState<Value<E, K>>(defaultValue);
  // used for seeding state when a builtin view is selected
  const [localState, setLocalState, resetLocalState] = useEntityStorage({
    entityType: entityStorageKey.entityType,
    entityIdentifier: entityStorageKey.identifier,
    key: entityStorageKey.key,
    defaultValue: entityStorageKey.defaultValue,
  });

  const updateState = useEvent((value: SetStateAction<Value<E, K>>) => {
    const nextValue = value instanceof Function ? value(state) : value;
    if (!IGNORED_URL_KEYS.has(viewField)) {
      setUrlState(nextValue ?? defaultValue ?? null);
    }
    setState(value);
    const hasView = currentView && !currentView.builtin;
    // Only use local storage for builtin views
    if (!hasView) {
      setLocalState(value);
    }
  });

  const navigationType = useNavigationType();
  const resetState = useEvent(
    (
      view: View | null,
      opts?: { initialize?: boolean; forceLocalState?: Value<E, K> },
    ) => {
      if (isPending) {
        return;
      }
      const initialize = opts?.initialize ?? false;
      const forceLocalState = opts?.forceLocalState;

      const hasView = view && !view.builtin;
      if (initialize) {
        if (urlState) {
          // If there is an initial url state, that means the user clicked on a link with
          // some url params. In this case we want to use the url state as truth
          setState(urlState);
          if (!hasView) {
            setLocalState(urlState);
          }
          return;
        }

        if (hasView) {
          resetLocalState();
          const viewValue = getViewValue<E, K>(view, viewField);
          if (viewValue) {
            setState(viewValue);
            return;
          }
          return;
        }

        if (
          !IGNORED_URL_KEYS.has(viewField) &&
          forceLocalState === undefined &&
          navigationType === "hard"
        ) {
          // Weird edge case where some people remove the url params and then hard reload the page
          // If we were to restore the url value from the localstorage value, then
          // removing the url params manually via the url bar will never reset the state.
          // So only use localstorage as a seed if the user is navigating
          resetLocalState();
          return;
        }

        // no url value, no view - seed from localstorage
        setState(forceLocalState ?? localState);
        if (!IGNORED_URL_KEYS.has(viewField)) {
          try {
            if (forceLocalState) {
              const nextValue = forceLocalState ?? localState ?? null;
              _setUrlState(isEqual(nextValue, defaultValue) ? null : nextValue);
            } else {
              setUrlState(localState ?? null);
            }
          } catch (e) {
            // In some legacy cases we have invalid stored view values, then setting the url could fail in the url parser.
            // This can happen if we change the underlying data type for the view field
            // In that case, just throw out the stored state
            console.warn(
              `Unable to set url state for view field ${viewField}:`,
              e,
            );
            setState(defaultValue);
          }
          return;
        }

        _setUrlState(null);
        return;
      }

      _setUrlState(null);
      resetLocalState();
      if (hasView) {
        const viewValue = getViewValue<E, K>(view, viewField);
        if (viewValue != null) {
          setState(viewValue);
          return;
        }
      }
      setState(defaultValue);
    },
  );

  const isDirty = useMemo(() => {
    return IGNORED_URL_KEYS.has(viewField)
      ? !isEqual(state ?? defaultValue, savedViewValue ?? defaultValue)
      : urlState != null;
  }, [viewField, savedViewValue, defaultValue, state, urlState]);
  return [
    state ?? savedViewValue ?? defaultValue,
    updateState,
    resetState,
    isDirty,
  ] as const;
}

function getViewValue<E extends Entity, K extends keyof EntityStorageSchema[E]>(
  view: View | null,
  viewField: keyof TableViewOptions | keyof MonitorViewOptions,
): Value<E, K> | undefined {
  const viewType = getObjValueByPath(view ?? {}, ["options", "viewType"]);
  const savedValue = // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    getObjValueByPath(view ?? {}, [
      "options",
      ...(viewType != null ? ["options"] : []),
      viewField,
    ]) as Value<E, K>;
  return savedValue;
}

function useViewSearchState({
  viewParams,
  pageIdentifier,
  clauseChecker,
  bypassClauseChecker,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  bypassClauseChecker?: boolean;
}) {
  const { data, isPending } = useViewQuery({ viewParams, pageIdentifier });
  const [viewName] = useQueryState("v", parseAsString);
  const navigationType = useNavigationType();

  // Local storage for search
  const [localSearch, setLocalSearch] = useEntityStorage({
    entityType: "tables",
    entityIdentifier: pageIdentifier,
    key: "search",
  });

  // Keep URL state for sharing/bookmarking
  const [urlSearch, setUrlSearch] = useSearchState();

  // for backwards compatibility reasons since we changed the encoding scheme
  // can remove probably after a couple of months
  const hasResetUrlSearch = useRef(false);
  useEffect(() => {
    if (!urlSearch || hasResetUrlSearch.current) {
      return;
    }
    setUrlSearch(urlSearch);
    hasResetUrlSearch.current = true;
  }, [urlSearch, setUrlSearch]);

  // Load legacy URL query params
  const [urlTags, setUrlTags] = useQueryState<string[]>(
    "tags",
    parseAsArrayOf(parseAsString),
  );
  const [urlClauses, setUrlClauses] = useQueryState<string[]>(
    "sb",
    parseAsArrayOf(parseAsString),
  );

  const {
    flags: { views: areViewsEnabled },
  } = useFeatureFlags();

  const [search, _setSearch] = useState<Search>({});
  // Can be used in actual queries so that we don't hammer the backend with filtering.
  // For example, used with the MATCH filter input bar on logs page.
  // This is akin to splitting up input text and search text
  const [searchDebounced, _setSearchDebounced] = useState<Search>({});
  const setSearchDebounced = useDebouncedCallback(_setSearchDebounced, 500);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Update both storage methods when search changes
  const updateBothStorages = useCallback(
    (newSearch: Search, currentView: View | null) => {
      // Update URL params
      const newUrlSearch = normalizeSearch(newSearch);
      setUrlSearch(
        isEqual(currentView?.view_data?.search, newUrlSearch)
          ? null
          : (newUrlSearch ??
              // always set a non-null value if there is a saved value
              (Object.keys(currentView?.view_data?.search ?? {}).length > 0
                ? {}
                : null)),
      );
      if (currentView?.builtin || !currentView) {
        setLocalSearch(newUrlSearch ?? {});
      }
    },
    [setUrlSearch, setLocalSearch],
  );

  // Initialize from either URL or local storage
  useEffect(() => {
    (async () => {
      if (
        (areViewsEnabled && isPending) ||
        hasInitialized ||
        (!bypassClauseChecker && !clauseChecker)
      ) {
        return;
      }

      // Only use local storage for builtin views
      const currentView = data?.find((v) => v.name === viewName);
      const urlOrLocalSearch =
        urlSearch ??
        // Weird edge case where some people remove the url params and then hard reload the page
        // If we were to restore the search value from localstorage,
        // then removing the url params will never reset the state.
        // So only use localstorage as a seed if the user is navigating
        ((currentView?.builtin || !currentView) && navigationType === "soft"
          ? localSearch
          : undefined);
      const storedSearch = urlOrLocalSearch ?? currentView?.view_data?.search;
      const storedSearchParsed = urlSearchSchema
        .nullish()
        .safeParse(storedSearch);
      if (!storedSearchParsed.success) {
        console.error(JSON.stringify(storedSearchParsed.error.errors, null, 2));
      }
      const storedSearchTyped = storedSearchParsed.success
        ? storedSearchParsed.data
        : undefined;

      const clauseSpecs: AnyClauseSpec[] = [
        urlValueToClauses("filter", storedSearchTyped?.filter),
        urlValueToClauses("tag", storedSearchTyped?.tag),
        urlValueToClauses("match", storedSearchTyped?.match),
        urlValueToClauses("sort", storedSearchTyped?.sort),
        (urlTags ?? []).map(
          (text): ClauseSpec<"tag"> => ({ type: "tag", text: `+${text}` }),
        ),
        (urlClauses ?? [])
          .map((text): AnyClauseSpec | undefined => {
            const types: ClauseType[] = ["filter", "match", "sort"];
            for (const type of types) {
              const prefix = `${type}:`;
              if (text.startsWith(prefix)) {
                return { type, text: text.slice(prefix.length) };
              }
            }
          })
          .filter((v): v is AnyClauseSpec => !!v),
      ].flat();

      const checkResults = bypassClauseChecker
        ? clauseSpecs.reduce<CheckResult<ClauseType>[]>((acc, clause) => {
            switch (clause.type) {
              case "filter":
              case "sort":
                acc.push({
                  type: "checked",
                  extraFields: { btql: { parsed: { btql: clause.text } } },
                });
                break;
              case "tag":
                acc.push(checkTag({ text: clause.text }));
                break;
              case "match":
                acc.push({ type: "checked", extraFields: {} });
                break;
            }
            return acc;
          }, [])
        : await Promise.all(
            clauseSpecs.map((clause) => clauseChecker?.(clause)),
          );

      const newSearch = checkResults.reduce((acc, checkResult, i) => {
        const clause = clauseSpecs[i];
        if (checkResult?.type !== "checked") {
          console.error(
            "Error processing URL search clause",
            clause,
            checkResult,
          );
          return acc;
        }

        return addClause(acc, {
          ...clause,
          ...checkResult.extraFields,
          bubble: makeBubble({
            clause,
            setSearch: (v) => {
              hasEdited.current = true;
              _setSearch(v);
              _setSearchDebounced(v);
            },
          }),
        });
        // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      }, {} as Search);
      _setSearch(newSearch);
      _setSearchDebounced(newSearch);

      // Clear legacy URL query params
      setUrlTags(null);
      setUrlClauses(null);

      if (currentView?.builtin || !currentView) {
        const newUrlSearch = normalizeUrlSearch(urlOrLocalSearch ?? {});
        const nextUrlSearch = isEqual(
          currentView?.view_data?.search ?? null,
          newUrlSearch,
        )
          ? null
          : newUrlSearch;
        setLocalSearch(nextUrlSearch ?? {});
        if (navigationType === "soft") {
          setUrlSearch(nextUrlSearch);
        }
      }

      setHasInitialized(true);
    })();
  }, [
    areViewsEnabled,
    isPending,
    hasInitialized,
    setHasInitialized,
    viewName,
    data,
    _setSearch,
    _setSearchDebounced,
    urlSearch,
    setUrlSearch,
    bypassClauseChecker,
    clauseChecker,
    urlTags,
    setUrlTags,
    urlClauses,
    setUrlClauses,
    localSearch,
    setLocalSearch,
    navigationType,
  ]);

  const hasEdited = useRef(false);
  const [isSearchTransitioning, startSearchTransition] = useTransition();
  const setSearch: Dispatch<SetStateAction<Search>> = useEvent(
    (valueOrUpdater) => {
      if (!hasInitialized) {
        return;
      }

      hasEdited.current = true;
      _setSearch(valueOrUpdater);

      const nextValue =
        typeof valueOrUpdater === "function"
          ? valueOrUpdater(search)
          : valueOrUpdater;
      const newUrlSearch = normalizeSearch(nextValue);
      const prevUrlSearch = normalizeSearch(search);
      // for now just debounce match queries
      if (!isEqual(newUrlSearch?.match, prevUrlSearch?.match)) {
        startSearchTransition(() => {
          setSearchDebounced(nextValue);
        });
      } else {
        _setSearchDebounced(nextValue);
      }
    },
  );

  useEffect(() => {
    const currentView = data?.find((v) => v.name === viewName);
    if (!hasInitialized || !hasEdited.current) {
      return;
    }

    updateBothStorages(search, currentView ?? null);
  }, [hasInitialized, data, viewName, search, updateBothStorages]);

  const resetSearch = useCallback(
    (currentView: View | null) => {
      setUrlSearch(null);
      if (!currentView || currentView.builtin) {
        const storedSearchParsed = urlSearchSchema
          .nullish()
          .safeParse(currentView?.view_data?.search);
        const storedSearch =
          (storedSearchParsed.success ? storedSearchParsed.data : null) ?? {};
        setLocalSearch(storedSearch);
      } else {
        setLocalSearch({});
      }
      setHasInitialized(false);
      hasEdited.current = false;
      return undefined;
    },
    [setUrlSearch, setLocalSearch, setHasInitialized],
  );

  return {
    search,
    searchDebounced,
    isSearchTransitioning,
    setSearch,
    resetSearch,
    isDirty: urlSearch != null,
  };
}

export const selectionTypeParser = parseSelectionTypeState();

export function useViewStates({
  pageIdentifier,
  viewParams,
  clauseChecker,
  bypassClauseChecker,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  bypassClauseChecker?: boolean;
}): ViewProps<false>;
export function useViewStates({
  pageIdentifier,
  viewParams,
  clauseChecker,
  experimentsChartingProps,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  experimentsChartingProps: {
    groupingDefaultSelectionType: SelectionType;
  };
}): ViewProps<true>;
export function useViewStates({
  pageIdentifier,
  viewParams,
  clauseChecker,
  experimentsChartingProps,
  bypassClauseChecker,
}: {
  pageIdentifier: string;
  viewParams: ViewParams | undefined;
  clauseChecker?: ClauseChecker | null;
  bypassClauseChecker?: boolean;
  experimentsChartingProps?: {
    groupingDefaultSelectionType: SelectionType;
  };
}): ViewProps<true> | ViewProps<false> {
  const [
    columnVisibility,
    setColumnVisibility,
    resetColumnVisibility,
    isColumnVisibilityDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "columnVisibility",
    },
    queryParamState: {
      queryKey: "_cv",
      parser: noopParser,
    },
    viewField: "columnVisibility",
    defaultValue: defaultEntityStorageValues.tables.columnVisibility,
  });
  const [columnOrder, setColumnOrder, resetColumnOrder, isColumnOrderDirty] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "tables",
        identifier: pageIdentifier,
        key: "columnOrderConfiguration",
      },
      queryParamState: {
        queryKey: "_co",
        parser: noopParser,
      },
      viewField: "columnOrder",
      defaultValue: defaultEntityStorageValues.tables.columnOrderConfiguration,
    });
  const [
    columnSizing,
    setColumnSizing,
    resetColumnSizing,
    isColumnSizingDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "columnWidthConfiguration",
    },
    queryParamState: {
      queryKey: "_cw",
      parser: noopParser,
    },
    viewField: "columnSizing",
    defaultValue: defaultEntityStorageValues.tables.columnWidthConfiguration,
  });

  const groupingDefaultSelectionType =
    experimentsChartingProps?.groupingDefaultSelectionType;
  const defaultGroupingStr = useMemo(() => {
    return groupingDefaultSelectionType
      ? selectionTypeParser.serialize(groupingDefaultSelectionType)
      : GROUP_BY_NONE_VALUE;
  }, [groupingDefaultSelectionType]);
  const [groupingStr, setGroupingStr, resetGrouping, isGroupingDirty] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "tables",
        identifier: pageIdentifier,
        key: "grouping",
      },
      queryParamState: {
        queryKey: "g",
        parser: parseAsString,
      },
      viewField: "grouping",
      defaultValue: defaultGroupingStr,
    });

  // since we use strings to store grouping types for all pages but the experiments list page,
  // we will continue to store strings values in localstorage but parse them when we come back
  const grouping = useMemo(() => {
    if (groupingDefaultSelectionType) {
      return groupingStr
        ? selectionTypeParser.parse(groupingStr)
        : groupingDefaultSelectionType;
    }

    return groupingStr;
  }, [groupingStr, groupingDefaultSelectionType]);
  const setGrouping = useMemo(() => {
    if (groupingDefaultSelectionType) {
      return (v: SetStateAction<SelectionType>) => {
        const next =
          typeof v === "function"
            ? v(
                (groupingStr ? selectionTypeParser.parse(groupingStr) : null) ??
                  groupingDefaultSelectionType,
              )
            : v;
        setGroupingStr(selectionTypeParser.serialize(next));
        return;
      };
    }

    return setGroupingStr;
  }, [groupingDefaultSelectionType, groupingStr, setGroupingStr]);

  const [rowHeight, setRowHeight, resetRowHeight, isRowHeightDirty] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "tables",
        identifier: pageIdentifier,
        key: "rowHeight",
      },
      queryParamState: {
        queryKey: "rh",
        parser: parseAsStringLiteral(TABLE_ROW_HEIGHTS),
      },
      viewField: "rowHeight",
      defaultValue: DEFAULT_TABLE_ROW_HEIGHT,
    });

  const [
    tallGroupRows,
    setTallGroupRows,
    resetTallGroupRows,
    isTallGroupRowsDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "tallGroupRows",
    },
    queryParamState: {
      queryKey: "tg",
      parser: parseAsBoolean,
    },
    viewField: "tallGroupRows",
    defaultValue: defaultEntityStorageValues.tables.tallGroupRows,
  });

  const defaultLayoutType =
    viewParams?.viewType === "playground" ? "grid" : null;
  const [layout, setLayout, resetLayout, isLayoutDirty] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "layout",
      defaultValue: defaultLayoutType,
    },
    queryParamState: {
      queryKey: "lt",
      parser: parseAsStringLiteral(tableLayoutTypes),
    },
    viewField: "layout",
    defaultValue: defaultLayoutType ?? "list",
  });

  const [chartHeight, setChartHeight, resetChartHeight, isChartHeightDirty] =
    useViewState({
      pageIdentifier,
      viewParams,
      entityStorageKey: {
        entityType: "charts",
        identifier: pageIdentifier,
        key: "height",
      },
      queryParamState: {
        queryKey: "ch",
        parser: parseAsInteger,
      },
      viewField: "chartHeight",
      defaultValue: defaultEntityStorageValues.charts.height,
    });

  function parseTimeRangeFilter(value: unknown): TimeRangeFilter {
    if (typeof value === "string") {
      return value;
    }

    if (
      value &&
      typeof value === "object" &&
      "from" in value &&
      "to" in value &&
      typeof value.from === "string" &&
      typeof value.to === "string"
    ) {
      return { from: value.from, to: value.to };
    }

    return "";
  }

  const [
    timeRangeFilter,
    setTimeRangeFilter,
    resetTimeRangeFilter,
    isTimeRangeFilterDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "tables",
      identifier: pageIdentifier,
      key: "timeRangeFilter",
      defaultValue: isViewFieldEnabled("timeRangeFilter", viewParams?.viewType)
        ? DEFAULT_TIME_RANGE_FILTER
        : undefined,
    },
    queryParamState: {
      queryKey: "range",
      parser: parseAsJsonEncoded(parseTimeRangeFilter),
    },
    viewField: "timeRangeFilter",
    defaultValue: DEFAULT_TIME_RANGE_FILTER,
  });

  // START - experiments page charting controls
  const [
    excludedMeasures,
    setExcludedMeasures,
    resetExcludedMeasures,
    isExcludedMeasuresDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "excludedMeasures",
    },
    queryParamState: {
      queryKey: "ye",
      parser: experimentsChartingProps
        ? parseAsArrayOf(parseSelectionTypeState())
        : noopParser,
    },
    viewField: "excludedMeasures",
    defaultValue: null,
  });
  const [yMetric, setYMetric, resetYMetric, isYMetricDirty] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "yMetric",
    },
    queryParamState: {
      queryKey: "y",
      parser: experimentsChartingProps ? parseSelectionTypeState() : noopParser,
    },
    viewField: "yMetric",
    defaultValue: null,
  });

  const [xAxis, setXAxis, resetXAxis, isXAxisDirty] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "xAxis",
    },
    queryParamState: {
      queryKey: "x",
      parser: experimentsChartingProps ? parseSelectionTypeState() : noopParser,
    },
    viewField: "xAxis",
    defaultValue: X_AXIS_EXPERIMENT,
  });

  const [
    symbolGrouping,
    setSymbolGrouping,
    resetSymbolGrouping,
    isSymbolGroupingDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "symbolGrouping",
    },
    queryParamState: {
      queryKey: "sg",
      parser: experimentsChartingProps ? parseSelectionTypeState() : noopParser,
    },
    viewField: "symbolGrouping",
    defaultValue: GROUP_BY_NONE,
  });

  const [
    xAxisAggregation,
    setXAxisAggregation,
    resetXAxisAggregation,
    isXAxisAggregationDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "xAxisAggregation",
    },
    queryParamState: {
      queryKey: "xa",
      parser: experimentsChartingProps
        ? parseAsStringLiteral([
            ...aggregationTypeEnumSchema.options,
            "all",
          ] as const)
        : noopParser,
    },
    viewField: "xAxisAggregation",
    defaultValue: DEFAULT_X_AXIS_AGGREGATION,
  });

  const [
    chartAnnotations,
    setChartAnnotations,
    resetChartAnnotations,
    isChartAnnotationsDirty,
  ] = useViewState({
    pageIdentifier,
    viewParams,
    entityStorageKey: {
      entityType: "charts",
      identifier: pageIdentifier,
      key: "annotations",
    },
    queryParamState: {
      queryKey: "ca",
      parser: experimentsChartingProps
        ? parseAsJson(annotationsSchema.parse)
        : noopParser,
    },
    viewField: "chartAnnotations",
    defaultValue: defaultEntityStorageValues.charts.annotations,
  });

  // END - experiments page charting controls

  const {
    search,
    searchDebounced,
    isSearchTransitioning,
    setSearch,
    resetSearch,
    isDirty: isSearchDirty,
  } = useViewSearchState({
    pageIdentifier,
    viewParams,
    clauseChecker,
    bypassClauseChecker,
  });

  const resetState = useEvent(
    (
      view: View | null,
      options?: { initialize?: boolean; reset?: boolean },
    ) => {
      const initialize = options?.initialize ?? false;
      resetColumnVisibility(view, options);
      resetColumnOrder(view, options);
      resetColumnSizing(view, options);
      resetGrouping(view, options);
      resetRowHeight(view, options);
      resetTallGroupRows(view, options);
      resetLayout(view, options);
      resetChartHeight(view, options);
      if (!initialize && !options?.reset && (!view || view.builtin)) {
        // a little hacky but force the url state when switching
        // from non-builtin biew to builtin view
        resetTimeRangeFilter(view, {
          initialize: true,
          forceLocalState: timeRangeFilter,
        });
      } else {
        resetTimeRangeFilter(view, options);
      }
      if (experimentsChartingProps) {
        resetExcludedMeasures(view, options);
        resetYMetric(view, options);
        resetXAxis(view, options);
        resetSymbolGrouping(view, options);
        resetXAxisAggregation(view, options);
        resetChartAnnotations(view, options);
      }
      if (!initialize) {
        resetSearch(view);
      }
    },
  );

  const { data, isPending } = useViewQuery({ viewParams, pageIdentifier });
  const [viewName] = useQueryState("v", parseAsString);
  const view = data?.find((v) => v.name === viewName);

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
  const viewOptions = useMemo(() => {
    return {
      columnVisibility,
      columnOrder,
      columnSizing,
      grouping,
      rowHeight,
      tallGroupRows,
      chartHeight,
      timeRangeFilter,
      layout,
      ...(experimentsChartingProps
        ? {
            excludedMeasures,
            yMetric,
            xAxis,
            symbolGrouping,
            xAxisAggregation,
            chartAnnotations,
          }
        : {}),
    };
  }, [
    columnVisibility,
    columnOrder,
    columnSizing,
    grouping,
    rowHeight,
    tallGroupRows,
    chartHeight,
    timeRangeFilter,
    experimentsChartingProps,
    excludedMeasures,
    yMetric,
    xAxis,
    symbolGrouping,
    xAxisAggregation,
    chartAnnotations,
    layout,
  ]) as typeof experimentsChartingProps extends never
    ? ViewOptionsFields<false>
    : ViewOptionsFields<true>;

  const isDirty = useMemo(() => {
    const areViewOptionsDirty = [
      isColumnVisibilityDirty,
      isColumnOrderDirty,
      isColumnSizingDirty,
      isGroupingDirty,
      isRowHeightDirty,
      isTallGroupRowsDirty,
      isLayoutDirty,
      isChartHeightDirty,
      isTimeRangeFilterDirty,
      ...(experimentsChartingProps
        ? [
            isExcludedMeasuresDirty,
            isYMetricDirty,
            isXAxisDirty,
            isSymbolGroupingDirty,
            isXAxisAggregationDirty,
            isChartAnnotationsDirty,
          ]
        : []),
    ].some(Boolean);

    return areViewOptionsDirty || isSearchDirty;
  }, [
    isColumnVisibilityDirty,
    isColumnOrderDirty,
    isColumnSizingDirty,
    isGroupingDirty,
    isRowHeightDirty,
    isTallGroupRowsDirty,
    isLayoutDirty,
    isChartHeightDirty,
    isTimeRangeFilterDirty,
    experimentsChartingProps,
    isExcludedMeasuresDirty,
    isYMetricDirty,
    isXAxisDirty,
    isSymbolGroupingDirty,
    isXAxisAggregationDirty,
    isChartAnnotationsDirty,
    isSearchDirty,
  ]);

  // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- not sure how to get this typing to play nice, so ended up using some hacky assertions
  return useMemo(
    () => ({
      ...viewOptions,
      search,
      searchDebounced,
      isSearchTransitioning,
      setColumnVisibility,
      setColumnOrder,
      setColumnSizing,
      setGrouping,
      setRowHeight,
      setTallGroupRows,
      setLayout,
      setSearch,
      setChartHeight,
      setTimeRangeFilter,
      isPending,
      pageIdentifier,
      viewParams,
      currentView: view,
      resetState,
      clauseChecker,
      isDirty,
      viewOptionsToSave: {
        ...viewOptions,
        grouping: groupingStr,
      },
      ...(experimentsChartingProps
        ? {
            setExcludedMeasures,
            setYMetric,
            setXAxis,
            setSymbolGrouping,
            setXAxisAggregation,
            setChartAnnotations,
          }
        : {}),
    }),
    [
      viewOptions,
      groupingStr,
      search,
      searchDebounced,
      isSearchTransitioning,
      clauseChecker,
      isPending,
      pageIdentifier,
      resetState,
      setColumnOrder,
      setColumnSizing,
      setColumnVisibility,
      setGrouping,
      setLayout,
      setRowHeight,
      setTallGroupRows,
      setChartHeight,
      setTimeRangeFilter,
      setSearch,
      view,
      viewParams,
      experimentsChartingProps,
      setExcludedMeasures,
      setYMetric,
      setXAxis,
      setSymbolGrouping,
      setXAxisAggregation,
      setChartAnnotations,
      isDirty,
    ],
  ) as typeof experimentsChartingProps extends never
    ? ViewProps<false>
    : ViewProps<true>;
}

export type ViewParams = {
  objectType: AclObjectType;
  objectId: string;
  viewType: ViewType;
};

export type GetViewsArgs = {
  apiUrl: string;
  getOrRefreshToken: () => Promise<BtSessionToken>;
  viewParams: ViewParams;
};

async function getViews({
  apiUrl,
  getOrRefreshToken,
  viewParams,
}: GetViewsArgs): Promise<View[] | null> {
  const sessionToken = await getOrRefreshToken();
  if (sessionToken === "loading") {
    throw new Error("Session token is still loading");
  }
  const { objectType, objectId, viewType } = viewParams;
  const params = new URLSearchParams({
    object_type: objectType,
    object_id: objectId,
    view_type: viewType,
  });
  const url = _urljoin(apiUrl, "/v1/view?") + new URLSearchParams(params);
  // NOTE: We use the legacy fetch here because the REST API doesn't support
  // CORS-optimized GET requests that use `"Content-Type": "text/plain"`.
  const resp = await apiFetchGetCors(url, sessionToken);
  if (!resp.ok) {
    throw new Error(await resp.text());
  }
  return (await resp.json()).objects;
}

export function useViewsQueryKey({
  pageIdentifier,
  getViewArgs,
}: {
  pageIdentifier: string;
  getViewArgs: Partial<GetViewsArgs>;
}) {
  const { getOrRefreshToken } = useSessionToken();
  return ["getViews", pageIdentifier, getViewArgs, getOrRefreshToken];
}

const NON_ERROR_VIEW = {
  id: "non_errors",
  builtin: true,
  name: "Non-errors",
  view_data: {
    search: {
      filter: [
        {
          text: "error IS NULL and is_root",
          label: "error IS NULL and is_root",
          originType: "btql",
        },
      ],
    },
  },
};

const ERROR_VIEW = {
  id: "errors",
  builtin: true,
  name: "Errors",
  view_data: {
    search: {
      filter: [
        {
          text: "error IS NOT NULL and is_root",
          label: "error IS NOT NULL and is_root",
          originType: "btql",
        },
      ],
    },
  },
};

const UNREVIEWED_VIEW = {
  id: "unreviewed",
  builtin: true,
  name: "Unreviewed",
};

const ASSIGNED_TO_ME_VIEW = {
  id: "assigned_to_me",
  builtin: true,
  name: "Assigned to me",
};

const BUILTIN_VIEWS: Partial<Record<ViewType, View[]>> = {
  logs: [NON_ERROR_VIEW, ERROR_VIEW, UNREVIEWED_VIEW, ASSIGNED_TO_ME_VIEW],
  experiment: [
    NON_ERROR_VIEW,
    ERROR_VIEW,
    UNREVIEWED_VIEW,
    ASSIGNED_TO_ME_VIEW,
  ],
  dataset: [ASSIGNED_TO_ME_VIEW],
};

export function useViewQuery({
  viewParams,
  pageIdentifier,
}: {
  viewParams: ViewParams | undefined;
  pageIdentifier: string;
}) {
  const { config } = useContext(ProjectContext);
  const { getOrRefreshToken } = useSessionToken();
  const org = useOrg();
  const { user } = useUser();
  const apiUrl = org.api_url;

  const {
    flags: { views: areViewsEnabled },
  } = useFeatureFlags();

  const queryKey = useViewsQueryKey({
    pageIdentifier,
    getViewArgs: {
      apiUrl,
      getOrRefreshToken,
      viewParams,
    },
  });

  const humanReviewScores = useMemo(
    () =>
      config.scores?.filter(
        (s) =>
          s.score_type === "categorical" ||
          s.score_type === "free-form" ||
          s.score_type === "slider",
      ) ?? [],
    [config.scores],
  );

  const builtInViews = useMemo(() => {
    if (!viewParams?.viewType) return [];

    return (BUILTIN_VIEWS[viewParams.viewType] ?? [])
      .filter((v) =>
        v.id === "unreviewed" ? humanReviewScores.length !== 0 : true,
      )
      .map((v) => {
        switch (v.id) {
          case "assigned_to_me":
            return {
              ...v,
              view_data: {
                search: {
                  filter: [
                    {
                      text: `${BT_ASSIGNMENTS} includes '${user?.id}'`,
                      label: `Assigned to me`,
                    },
                  ],
                },
              },
            };
          case "unreviewed":
            return {
              ...v,
              view_data: {
                search: {
                  filter: [
                    humanReviewScores
                      .map((s) => `scores.${doubleQuote(s.name)} IS NULL`)
                      .join(" AND "),
                  ],
                },
              },
            };
          default:
            return v;
        }
      });
  }, [viewParams?.viewType, humanReviewScores, user?.id]);

  const { data, isPending } = useQuery({
    queryKey,
    queryFn: async () => {
      const sessionToken = await getOrRefreshToken();
      // in public pages we want to fake successfully loading views so we can set up the clause checker
      // and support filtering UI
      return sessionToken === "unauthenticated"
        ? []
        : await getViews({
            apiUrl,
            getOrRefreshToken,
            viewParams: viewParams!,
          });
    },
    enabled: areViewsEnabled && !!apiUrl && !!viewParams?.objectId,
    meta: {
      disableGlobalErrorToast: true,
      globalErrorSentryContext: {
        tags: {
          feature: "views",
          action: "get",
        },
      },
    },
  });

  const views = useMemo(() => {
    return builtInViews.concat(data ?? []);
  }, [builtInViews, data]);

  return { data: views, isPending };
}

export const makeRequiredPatchParams = (viewParams: ViewParams | undefined) => {
  if (!viewParams) {
    throw new Error("Cannot update view without viewParams");
  }

  return {
    object_type: viewParams.objectType,
    object_id: viewParams.objectId,
  };
};
